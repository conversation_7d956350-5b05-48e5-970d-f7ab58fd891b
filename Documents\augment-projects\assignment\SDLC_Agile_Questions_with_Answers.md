# SDLC and Agile Methodology - Questions with Answers

## Multiple Choice Questions (20)

**1. What does SDLC stand for?**
a) Software Development Life Cycle
b) System Design Life Cycle
c) Software Design Life Course
d) System Development Life Course

**Answer: a) Software Development Life Cycle**

---

**2. What is the correct sequence of phases in the traditional SDLC?**
a) Design → Requirement → Coding → Testing
b) Requirement → Design → Coding → Testing
c) Coding → Design → Requirement → Testing
d) Testing → Coding → Design → Requirement

**Answer: b) Requirement → Design → Coding → Testing**

---

**3. In the Waterfall model, what happens if you want to change something?**
a) You can easily modify any phase
b) You must start from Step 1
c) You only need to modify the current step
d) Changes are not allowed

**Answer: b) You must start from Step 1**

---

**4. Can the Waterfall model be used in modern software development?**
a) No, it's completely obsolete
b) Yes, if the requirements don't change
c) Only for small projects
d) Only for web development

**Answer: b) Yes, if the requirements don't change**

---

**5. Which of the following is an example mentioned for stable requirements?**
a) Social media apps
b) Traffic lights and airlines
c) Mobile games
d) E-commerce websites

**Answer: b) Traffic lights and airlines**

---

**6. How many team operations are mentioned in Agile?**
a) 1
b) 2
c) 3
d) 4

**Answer: b) 2**

---

**7. Which of the following is NOT mentioned as an Agile methodology?**
a) Scrum
b) Kanban
c) XP
d) Waterfall

**Answer: d) Waterfall**

---

**8. What percentage is associated with Scrum in the notes?**
a) 70%
b) 80%
c) 90%
d) 100%

**Answer: b) 80%**

---

**9. What does "Product Backlog" contain?**
a) Completed features
b) All collected features
c) Only important features
d) Testing results

**Answer: b) All collected features**

---

**10. What does "Sprint BL" refer to?**
a) Sprint Baseline
b) Sprint Backlog with important features
c) Sprint Build List
d) Sprint Bug Log

**Answer: b) Sprint Backlog with important features**

---

**11. How many sprints are shown in the diagram?**
a) 2
b) 3
c) 4
d) 5

**Answer: b) 3**

---

**12. What is the cycle within each sprint?**
a) Req → Des → Build → Test
b) Plan → Code → Test → Deploy
c) Design → Code → Test → Review
d) Requirement → Code → Build → Release

**Answer: a) Req → Des → Build → Test**

---

**13. What happens in Sprint 1 according to the notes?**
a) All features are completed
b) The customer is shown progress
c) Testing is finalized
d) Requirements are gathered

**Answer: b) The customer is shown progress**

---

**14. When are updates implemented in the Agile process?**
a) Only at the end of all sprints
b) In Sprint 2 if there are customer updates
c) Never during the process
d) Only in Sprint 1

**Answer: b) In Sprint 2 if there are customer updates**

---

**15. What is the main characteristic of Agile work?**
a) Individual work
b) Team work
c) Management work
d) Client work

**Answer: b) Team work**

---

**16. What type of product should Agile focus on according to the notes?**
a) Complex product
b) Simple product
c) Minimal Viable Product
d) Complete product

**Answer: c) Minimal Viable Product**

---

**17. What is mentioned as an example requirement?**
a) User login
b) Sign up and payment
c) Database design
d) User interface

**Answer: b) Sign up and payment**

---

**18. In the Waterfall model, what is the main problem mentioned?**
a) It's too fast
b) It's too expensive
c) If you want to change anything, you start from Step 1
d) It requires too many people

**Answer: c) If you want to change anything, you start from Step 1**

---

**19. What makes Agile different from Waterfall in terms of changes?**
a) Agile doesn't allow changes
b) Agile accommodates changes between sprints
c) Agile is slower with changes
d) Agile requires more documentation for changes

**Answer: b) Agile accommodates changes between sprints**

---

**20. What is the main advantage of showing progress to customers in Sprint 1?**
a) To get final approval
b) To collect feedback for updates in Sprint 2
c) To end the project early
d) To reduce costs

**Answer: b) To collect feedback for updates in Sprint 2**

---

## Classic Questions (10)

**1. Explain the fundamental differences between the Waterfall model and Agile methodology in software development. Provide specific examples of when each approach would be most appropriate.**

**Answer:** The Waterfall model follows a sequential approach (Requirement → Design → Coding → Testing) where each phase must be completed before moving to the next. If changes are needed, you must start from Step 1. This works well for projects with stable requirements like traffic lights and airline systems. Agile methodology uses iterative sprints with continuous customer feedback, allowing changes between sprints. It focuses on team work and Minimal Viable Products, making it suitable for projects with evolving requirements.

---

**2. Describe the complete Agile sprint cycle and explain how customer feedback is incorporated into the development process.**

**Answer:** Each sprint follows the cycle: Req → Des → Build → Test. The Product Backlog contains all collected features, while Sprint Backlog contains important features for the current sprint. In Sprint 1, progress is shown to customers. Any customer updates or feedback are implemented in Sprint 2. This iterative approach allows continuous improvement and adaptation based on customer needs.

---

**3. Analyze the advantages and disadvantages of the Waterfall model. Why might some projects still use this approach despite its limitations?**

**Answer:** Advantages: Clear structure, well-defined phases, good for stable requirements. Disadvantages: Inflexible to changes (must start from Step 1), no customer feedback until the end. Projects with stable, well-understood requirements (like traffic lights, airline systems) still use Waterfall because the requirements don't change and the sequential approach provides clear milestones.

---

**4. What is a Minimal Viable Product (MVP) in Agile development, and how does it relate to the concept of Sprint Backlog and Product Backlog?**

**Answer:** MVP is the core focus of Agile development - creating a basic version with essential features first. The Product Backlog contains all collected features, while Sprint Backlog selects the most important features for each sprint. This prioritization helps build the MVP incrementally, delivering value early and getting customer feedback.

---

**5. Compare and contrast Scrum, Kanban, and XP methodologies. What are the key characteristics that distinguish each approach?**

**Answer:** All three are Agile methodologies mentioned in the notes. Scrum (80% usage) uses structured sprints with defined roles and ceremonies. Kanban focuses on continuous flow and visual management. XP emphasizes technical practices and code quality. All support team work and iterative development but differ in their specific practices and structures.

---

**6. Explain the role of team operations in Agile development. How does teamwork contribute to the success of Agile projects?**

**Answer:** Agile emphasizes "team work" as a core principle. The notes mention "2 team operations" in Agile. Teamwork enables collaborative problem-solving, shared responsibility, continuous communication, and collective ownership of the product. This collaborative approach contrasts with traditional individual-focused development and enables faster adaptation to changes.

---

**7. Discuss the importance of requirement stability in choosing between Waterfall and Agile methodologies. Provide real-world examples to support your answer.**

**Answer:** Requirement stability is crucial for methodology selection. Waterfall works when requirements are stable and unlikely to change (traffic lights, airline systems). Agile is better when requirements evolve (sign up and payment systems, user-facing applications). The key difference is that Waterfall requires starting from Step 1 for any changes, while Agile accommodates changes between sprints.

---

**8. Describe the sprint planning process and explain how features move from the Product Backlog to Sprint Backlog. What criteria should be used for prioritization?**

**Answer:** Features start in the Product Backlog (all collected features) and important features are selected for the Sprint Backlog. The selection should prioritize features that contribute to the Minimal Viable Product, provide customer value, and can be completed within the sprint cycle (Req → Des → Build → Test).

---

**9. Evaluate the statement "Agile is team work." How does this collaborative approach impact project outcomes compared to traditional development methods?**

**Answer:** This statement emphasizes Agile's collaborative nature. Team work enables shared knowledge, collective problem-solving, and continuous communication. Unlike traditional methods where individuals work in isolation, Agile teams work together throughout the sprint cycle, leading to better quality, faster problem resolution, and improved adaptability to changes.

---

**10. Analyze the iterative nature of Agile development. How does the cycle of Requirement → Design → Build → Test within each sprint contribute to overall project success?**

**Answer:** The iterative Req → Des → Build → Test cycle within each sprint ensures continuous validation and improvement. Each iteration produces working software that can be shown to customers (as in Sprint 1), enabling early feedback and course correction. This reduces risk, ensures customer satisfaction, and allows for updates to be implemented in subsequent sprints (Sprint 2), leading to better final outcomes.
