# Algorithm Verification

## Input Data Analysis
From input.txt:
```
10:1:0  -> Process 0: BurstTime=10, Priority=1, QueueID=0
5:0:0   -> Process 1: BurstTime=5,  Priority=0, QueueID=0
3:2:0   -> Process 2: BurstTime=3,  Priority=2, QueueID=0
2:1:0   -> Process 3: BurstTime=2,  Priority=1, QueueID=0
3:1:1   -> Process 4: BurstTime=3,  Priority=1, QueueID=1
10:0:1  -> Process 5: BurstTime=10, Priority=0, QueueID=1
2:2:1   -> Process 6: BurstTime=2,  Priority=2, QueueID=1
```

## Queue 0 Processes
- Process 0: BurstTime=10, Priority=1
- Process 1: BurstTime=5,  Priority=0
- Process 2: BurstTime=3,  Priority=2
- Process 3: BurstTime=2,  Priority=1

### FCFS (Algorithm 1) - Queue 0
Order: P0, P1, P2, P3 (file order)
- P0: WT = 0 (starts immediately)
- P1: WT = 10 (waits for P0)
- P2: WT = 15 (waits for P0+P1)
- P3: WT = 18 (waits for P0+P1+P2)
Average WT = (0+10+15+18)/4 = 10.75

### SJF (Algorithm 2) - Queue 0
Order by burst time: P3(2), P2(3), P1(5), P0(10)
- P3: WT = 0
- P2: WT = 2
- P1: WT = 5
- P0: WT = 10
Average WT = (0+2+5+10)/4 = 4.25

### Priority (Algorithm 3) - Queue 0
Order by priority (lower=higher): P1(0), P0(1), P3(1), P2(2)
- P1: WT = 0
- P0: WT = 5
- P3: WT = 15
- P2: WT = 17
Average WT = (0+5+15+17)/4 = 9.25

## Queue 1 Processes
- Process 4: BurstTime=3,  Priority=1
- Process 5: BurstTime=10, Priority=0
- Process 6: BurstTime=2,  Priority=2

### FCFS (Algorithm 1) - Queue 1
Order: P4, P5, P6 (file order)
- P4: WT = 0
- P5: WT = 3
- P6: WT = 13
Average WT = (0+3+13)/3 = 5.33

### SJF (Algorithm 2) - Queue 1
Order by burst time: P6(2), P4(3), P5(10)
- P6: WT = 0
- P4: WT = 2
- P5: WT = 5
Average WT = (0+2+5)/3 = 2.33

### Priority (Algorithm 3) - Queue 1
Order by priority: P5(0), P4(1), P6(2)
- P5: WT = 0
- P4: WT = 10
- P6: WT = 13
Average WT = (0+10+13)/3 = 7.67

## Expected Output Format
```
0:1:0:10:15:18:10.75
0:2:0:2:5:10:4.25
0:3:0:5:15:17:9.25
1:1:0:3:13:5.33
1:2:0:2:5:2.33
1:3:0:10:13:7.67
```
