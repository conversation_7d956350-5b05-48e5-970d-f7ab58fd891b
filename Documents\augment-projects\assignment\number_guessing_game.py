# Number Guessing Game
# The computer picks a random number, and you try to guess it!

import random

def number_guessing_game():
    print("🎮 Welcome to the Number Guessing Game! 🎮")
    print("I'm thinking of a number between 1 and 100...")
    print("Can you guess it?")
    
    # Computer picks a random number
    secret_number = random.randint(1, 100)
    attempts = 0
    max_attempts = 7
    
    while attempts < max_attempts:
        try:
            # Get player's guess
            guess = int(input(f"\nAttempt {attempts + 1}/{max_attempts} - Enter your guess: "))
            attempts += 1
            
            # Check the guess
            if guess == secret_number:
                print(f"🎉 Congratulations! You guessed it in {attempts} attempts!")
                print(f"The number was {secret_number}")
                return
            elif guess < secret_number:
                print("📈 Too low! Try a higher number.")
            else:
                print("📉 Too high! Try a lower number.")
                
            # Give hints based on how close they are
            difference = abs(guess - secret_number)
            if difference <= 5:
                print("🔥 You're very close!")
            elif difference <= 15:
                print("🌡️ You're getting warm!")
            else:
                print("🧊 You're cold!")
                
        except ValueError:
            print("❌ Please enter a valid number!")
            attempts -= 1  # Don't count invalid input as an attempt
    
    # Game over
    print(f"\n💀 Game Over! You ran out of attempts.")
    print(f"The secret number was {secret_number}")

# Start the game
if __name__ == "__main__":
    number_guessing_game()
    
    # Ask if they want to play again
    while True:
        play_again = input("\nDo you want to play again? (yes/no): ").lower()
        if play_again in ['yes', 'y']:
            number_guessing_game()
        elif play_again in ['no', 'n']:
            print("Thanks for playing! 👋")
            break
        else:
            print("Please enter 'yes' or 'no'")
