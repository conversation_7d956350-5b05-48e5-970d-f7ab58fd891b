"""
SADIS - Food Sales Management Application
========================================
A comprehensive food sales management system with inventory, orders, and reporting.
"""

import json
import datetime
from typing import Dict, List, Optional
import os

class FoodItem:
    """Represents a food item in the inventory"""
    
    def __init__(self, item_id: str, name: str, price: float, category: str, 
                 stock: int = 0, description: str = ""):
        self.item_id = item_id
        self.name = name
        self.price = price
        self.category = category
        self.stock = stock
        self.description = description
        self.created_date = datetime.datetime.now()
    
    def to_dict(self) -> Dict:
        """Convert food item to dictionary"""
        return {
            'item_id': self.item_id,
            'name': self.name,
            'price': self.price,
            'category': self.category,
            'stock': self.stock,
            'description': self.description,
            'created_date': self.created_date.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict):
        """Create food item from dictionary"""
        item = cls(
            data['item_id'], data['name'], data['price'],
            data['category'], data['stock'], data['description']
        )
        if 'created_date' in data:
            item.created_date = datetime.datetime.fromisoformat(data['created_date'])
        return item

class Order:
    """Represents a customer order"""
    
    def __init__(self, order_id: str, customer_name: str, customer_phone: str = ""):
        self.order_id = order_id
        self.customer_name = customer_name
        self.customer_phone = customer_phone
        self.items: List[Dict] = []  # {'item_id': str, 'quantity': int, 'price': float}
        self.total_amount = 0.0
        self.status = "pending"  # pending, confirmed, preparing, ready, delivered, cancelled
        self.order_date = datetime.datetime.now()
        self.delivery_address = ""
        self.notes = ""
    
    def add_item(self, item_id: str, quantity: int, price: float):
        """Add item to order"""
        self.items.append({
            'item_id': item_id,
            'quantity': quantity,
            'price': price,
            'subtotal': quantity * price
        })
        self.calculate_total()
    
    def calculate_total(self):
        """Calculate total order amount"""
        self.total_amount = sum(item['subtotal'] for item in self.items)
    
    def to_dict(self) -> Dict:
        """Convert order to dictionary"""
        return {
            'order_id': self.order_id,
            'customer_name': self.customer_name,
            'customer_phone': self.customer_phone,
            'items': self.items,
            'total_amount': self.total_amount,
            'status': self.status,
            'order_date': self.order_date.isoformat(),
            'delivery_address': self.delivery_address,
            'notes': self.notes
        }
    
    @classmethod
    def from_dict(cls, data: Dict):
        """Create order from dictionary"""
        order = cls(data['order_id'], data['customer_name'], data.get('customer_phone', ''))
        order.items = data['items']
        order.total_amount = data['total_amount']
        order.status = data['status']
        order.order_date = datetime.datetime.fromisoformat(data['order_date'])
        order.delivery_address = data.get('delivery_address', '')
        order.notes = data.get('notes', '')
        return order

class SadisApp:
    """Main SADIS Food Sales Application"""
    
    def __init__(self):
        self.food_items: Dict[str, FoodItem] = {}
        self.orders: Dict[str, Order] = {}
        self.data_file = "sadis_data.json"
        self.load_data()
        self.setup_sample_data()
    
    def load_data(self):
        """Load data from JSON file"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Load food items
                for item_data in data.get('food_items', []):
                    item = FoodItem.from_dict(item_data)
                    self.food_items[item.item_id] = item
                
                # Load orders
                for order_data in data.get('orders', []):
                    order = Order.from_dict(order_data)
                    self.orders[order.order_id] = order
                
                print("✅ Data loaded successfully!")
            except Exception as e:
                print(f"❌ Error loading data: {e}")
    
    def save_data(self):
        """Save data to JSON file"""
        try:
            data = {
                'food_items': [item.to_dict() for item in self.food_items.values()],
                'orders': [order.to_dict() for order in self.orders.values()]
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print("✅ Data saved successfully!")
        except Exception as e:
            print(f"❌ Error saving data: {e}")
    
    def setup_sample_data(self):
        """Setup sample food items if database is empty"""
        if not self.food_items:
            sample_items = [
                ("F001", "Margherita Pizza", 12.99, "Pizza", 20, "Classic pizza with tomato sauce, mozzarella, and basil"),
                ("F002", "Chicken Burger", 8.99, "Burgers", 15, "Grilled chicken breast with lettuce, tomato, and mayo"),
                ("F003", "Caesar Salad", 7.99, "Salads", 10, "Fresh romaine lettuce with Caesar dressing and croutons"),
                ("F004", "Spaghetti Carbonara", 11.99, "Pasta", 12, "Creamy pasta with bacon, eggs, and parmesan cheese"),
                ("F005", "Fish & Chips", 13.99, "Main Course", 8, "Battered fish with crispy fries and tartar sauce"),
                ("F006", "Chocolate Cake", 5.99, "Desserts", 6, "Rich chocolate cake with chocolate frosting"),
                ("F007", "Fresh Orange Juice", 3.99, "Beverages", 25, "Freshly squeezed orange juice"),
                ("F008", "Cappuccino", 4.49, "Beverages", 30, "Italian coffee with steamed milk foam"),
            ]
            
            for item_data in sample_items:
                item = FoodItem(*item_data)
                self.food_items[item.item_id] = item
            
            print("🍕 Sample food items added!")
    
    def display_menu(self):
        """Display main menu"""
        print("\n" + "="*50)
        print("🍕 SADIS - Food Sales Management System")
        print("="*50)
        print("1. 📋 View Menu")
        print("2. ➕ Add Food Item")
        print("3. ✏️  Edit Food Item")
        print("4. 🛒 Create New Order")
        print("5. 📦 View Orders")
        print("6. 🔄 Update Order Status")
        print("7. 📊 Sales Report")
        print("8. 📈 Inventory Report")
        print("9. 💾 Save Data")
        print("0. 🚪 Exit")
        print("="*50)
    
    def view_menu(self):
        """Display all food items"""
        print("\n🍽️  SADIS RESTAURANT MENU")
        print("="*60)
        
        if not self.food_items:
            print("❌ No food items available!")
            return
        
        # Group by category
        categories = {}
        for item in self.food_items.values():
            if item.category not in categories:
                categories[item.category] = []
            categories[item.category].append(item)
        
        for category, items in categories.items():
            print(f"\n📂 {category.upper()}")
            print("-" * 40)
            for item in items:
                stock_status = "✅ In Stock" if item.stock > 0 else "❌ Out of Stock"
                print(f"🆔 {item.item_id} | {item.name}")
                print(f"   💰 ${item.price:.2f} | 📦 Stock: {item.stock} | {stock_status}")
                print(f"   📝 {item.description}")
                print()
    
    def add_food_item(self):
        """Add new food item"""
        print("\n➕ ADD NEW FOOD ITEM")
        print("-" * 30)
        
        try:
            item_id = input("🆔 Item ID: ").strip().upper()
            if item_id in self.food_items:
                print("❌ Item ID already exists!")
                return
            
            name = input("🍕 Item Name: ").strip()
            price = float(input("💰 Price ($): "))
            category = input("📂 Category: ").strip()
            stock = int(input("📦 Initial Stock: "))
            description = input("📝 Description: ").strip()
            
            item = FoodItem(item_id, name, price, category, stock, description)
            self.food_items[item_id] = item
            
            print(f"✅ Food item '{name}' added successfully!")
            
        except ValueError:
            print("❌ Invalid input! Please enter correct values.")
        except Exception as e:
            print(f"❌ Error adding item: {e}")
    
    def edit_food_item(self):
        """Edit existing food item"""
        print("\n✏️  EDIT FOOD ITEM")
        print("-" * 20)
        
        item_id = input("🆔 Enter Item ID to edit: ").strip().upper()
        
        if item_id not in self.food_items:
            print("❌ Item not found!")
            return
        
        item = self.food_items[item_id]
        print(f"\nEditing: {item.name}")
        print("(Press Enter to keep current value)")
        
        try:
            # Edit fields
            new_name = input(f"🍕 Name [{item.name}]: ").strip()
            if new_name:
                item.name = new_name
            
            new_price = input(f"💰 Price [${item.price:.2f}]: ").strip()
            if new_price:
                item.price = float(new_price)
            
            new_category = input(f"📂 Category [{item.category}]: ").strip()
            if new_category:
                item.category = new_category
            
            new_stock = input(f"📦 Stock [{item.stock}]: ").strip()
            if new_stock:
                item.stock = int(new_stock)
            
            new_description = input(f"📝 Description [{item.description}]: ").strip()
            if new_description:
                item.description = new_description
            
            print("✅ Food item updated successfully!")
            
        except ValueError:
            print("❌ Invalid input! Changes not saved.")
        except Exception as e:
            print(f"❌ Error updating item: {e}")
    
    def create_order(self):
        """Create new customer order"""
        print("\n🛒 CREATE NEW ORDER")
        print("-" * 25)
        
        try:
            # Generate order ID
            order_id = f"ORD{len(self.orders) + 1:04d}"
            
            customer_name = input("👤 Customer Name: ").strip()
            customer_phone = input("📞 Phone Number: ").strip()
            
            order = Order(order_id, customer_name, customer_phone)
            
            print(f"\n📋 Order ID: {order_id}")
            print("Add items to order (type 'done' to finish):")
            
            while True:
                print("\nAvailable items:")
                for item_id, item in self.food_items.items():
                    if item.stock > 0:
                        print(f"  {item_id}: {item.name} - ${item.price:.2f} (Stock: {item.stock})")
                
                item_input = input("\n🆔 Item ID (or 'done'): ").strip().upper()
                
                if item_input == 'DONE':
                    break
                
                if item_input not in self.food_items:
                    print("❌ Invalid item ID!")
                    continue
                
                item = self.food_items[item_input]
                
                if item.stock <= 0:
                    print("❌ Item out of stock!")
                    continue
                
                quantity = int(input(f"📦 Quantity (max {item.stock}): "))
                
                if quantity > item.stock:
                    print("❌ Not enough stock!")
                    continue
                
                # Add item to order and update stock
                order.add_item(item_input, quantity, item.price)
                item.stock -= quantity
                
                print(f"✅ Added {quantity}x {item.name} to order")
            
            if order.items:
                # Additional order details
                order.delivery_address = input("🏠 Delivery Address (optional): ").strip()
                order.notes = input("📝 Special Notes (optional): ").strip()
                
                self.orders[order_id] = order
                
                print(f"\n✅ Order created successfully!")
                print(f"📋 Order ID: {order_id}")
                print(f"💰 Total Amount: ${order.total_amount:.2f}")
            else:
                print("❌ No items added to order!")
                
        except ValueError:
            print("❌ Invalid input!")
        except Exception as e:
            print(f"❌ Error creating order: {e}")
    
    def view_orders(self):
        """View all orders"""
        print("\n📦 ALL ORDERS")
        print("="*50)
        
        if not self.orders:
            print("❌ No orders found!")
            return
        
        for order in self.orders.values():
            status_emoji = {
                "pending": "⏳", "confirmed": "✅", "preparing": "👨‍🍳",
                "ready": "🍽️", "delivered": "🚚", "cancelled": "❌"
            }
            
            print(f"\n📋 Order: {order.order_id} | {status_emoji.get(order.status, '❓')} {order.status.upper()}")
            print(f"👤 Customer: {order.customer_name}")
            if order.customer_phone:
                print(f"📞 Phone: {order.customer_phone}")
            print(f"📅 Date: {order.order_date.strftime('%Y-%m-%d %H:%M')}")
            print(f"💰 Total: ${order.total_amount:.2f}")
            
            print("🛒 Items:")
            for item_data in order.items:
                item = self.food_items.get(item_data['item_id'])
                item_name = item.name if item else "Unknown Item"
                print(f"   • {item_data['quantity']}x {item_name} - ${item_data['subtotal']:.2f}")
            
            if order.delivery_address:
                print(f"🏠 Address: {order.delivery_address}")
            if order.notes:
                print(f"📝 Notes: {order.notes}")
            print("-" * 50)
    
    def update_order_status(self):
        """Update order status"""
        print("\n🔄 UPDATE ORDER STATUS")
        print("-" * 25)
        
        order_id = input("📋 Enter Order ID: ").strip().upper()
        
        if order_id not in self.orders:
            print("❌ Order not found!")
            return
        
        order = self.orders[order_id]
        
        print(f"\nCurrent status: {order.status}")
        print("\nAvailable statuses:")
        statuses = ["pending", "confirmed", "preparing", "ready", "delivered", "cancelled"]
        for i, status in enumerate(statuses, 1):
            print(f"{i}. {status}")
        
        try:
            choice = int(input("\nSelect new status (1-6): "))
            if 1 <= choice <= 6:
                order.status = statuses[choice - 1]
                print(f"✅ Order status updated to: {order.status}")
            else:
                print("❌ Invalid choice!")
        except ValueError:
            print("❌ Invalid input!")
    
    def sales_report(self):
        """Generate sales report"""
        print("\n📊 SALES REPORT")
        print("="*40)
        
        if not self.orders:
            print("❌ No orders to report!")
            return
        
        # Calculate statistics
        total_orders = len(self.orders)
        total_revenue = sum(order.total_amount for order in self.orders.values())
        
        # Orders by status
        status_count = {}
        for order in self.orders.values():
            status_count[order.status] = status_count.get(order.status, 0) + 1
        
        # Top selling items
        item_sales = {}
        for order in self.orders.values():
            for item_data in order.items:
                item_id = item_data['item_id']
                quantity = item_data['quantity']
                item_sales[item_id] = item_sales.get(item_id, 0) + quantity
        
        print(f"📈 Total Orders: {total_orders}")
        print(f"💰 Total Revenue: ${total_revenue:.2f}")
        print(f"📊 Average Order Value: ${total_revenue/total_orders:.2f}" if total_orders > 0 else "")
        
        print(f"\n📋 Orders by Status:")
        for status, count in status_count.items():
            print(f"   {status.capitalize()}: {count}")
        
        if item_sales:
            print(f"\n🏆 Top Selling Items:")
            sorted_items = sorted(item_sales.items(), key=lambda x: x[1], reverse=True)
            for item_id, quantity in sorted_items[:5]:
                item = self.food_items.get(item_id)
                item_name = item.name if item else "Unknown Item"
                print(f"   {item_name}: {quantity} sold")
    
    def inventory_report(self):
        """Generate inventory report"""
        print("\n📈 INVENTORY REPORT")
        print("="*40)
        
        if not self.food_items:
            print("❌ No items in inventory!")
            return
        
        # Low stock items (stock < 5)
        low_stock = [item for item in self.food_items.values() if item.stock < 5]
        out_of_stock = [item for item in self.food_items.values() if item.stock == 0]
        
        total_items = len(self.food_items)
        total_value = sum(item.price * item.stock for item in self.food_items.values())
        
        print(f"📦 Total Items: {total_items}")
        print(f"💰 Total Inventory Value: ${total_value:.2f}")
        
        if out_of_stock:
            print(f"\n❌ Out of Stock ({len(out_of_stock)} items):")
            for item in out_of_stock:
                print(f"   • {item.name} ({item.item_id})")
        
        if low_stock:
            print(f"\n⚠️  Low Stock ({len(low_stock)} items):")
            for item in low_stock:
                print(f"   • {item.name} ({item.item_id}) - Stock: {item.stock}")
        
        # Stock by category
        category_stock = {}
        for item in self.food_items.values():
            if item.category not in category_stock:
                category_stock[item.category] = {'items': 0, 'total_stock': 0}
            category_stock[item.category]['items'] += 1
            category_stock[item.category]['total_stock'] += item.stock
        
        print(f"\n📂 Stock by Category:")
        for category, data in category_stock.items():
            print(f"   {category}: {data['items']} items, {data['total_stock']} total stock")
    
    def run(self):
        """Main application loop"""
        print("🍕 Welcome to SADIS Food Sales Management System!")
        
        while True:
            self.display_menu()
            
            try:
                choice = input("\n🔢 Enter your choice (0-9): ").strip()
                
                if choice == '1':
                    self.view_menu()
                elif choice == '2':
                    self.add_food_item()
                elif choice == '3':
                    self.edit_food_item()
                elif choice == '4':
                    self.create_order()
                elif choice == '5':
                    self.view_orders()
                elif choice == '6':
                    self.update_order_status()
                elif choice == '7':
                    self.sales_report()
                elif choice == '8':
                    self.inventory_report()
                elif choice == '9':
                    self.save_data()
                elif choice == '0':
                    self.save_data()
                    print("👋 Thank you for using SADIS! Goodbye!")
                    break
                else:
                    print("❌ Invalid choice! Please try again.")
                
                input("\n⏸️  Press Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                self.save_data()
                break
            except Exception as e:
                print(f"❌ An error occurred: {e}")

if __name__ == "__main__":
    app = SadisApp()
    app.run()
