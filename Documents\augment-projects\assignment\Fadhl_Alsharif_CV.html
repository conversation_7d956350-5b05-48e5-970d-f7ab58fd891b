<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON><PERSON> - CV</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>ri', 'Arial', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #ecf0f1;
        }

        .container {
            max-width: 210mm;
            margin: 20px auto;
            background: white;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            border-radius: 12px;
            overflow: hidden;
            min-height: 297mm;
        }

        .header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 50px 40px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
        }

        .header-content {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .profile-photo {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            border: 5px solid rgba(255,255,255,0.3);
            object-fit: cover;
            flex-shrink: 0;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        }

        .header-info {
            flex: 1;
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 12px;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .header .subtitle {
            font-size: 1.3em;
            margin-bottom: 25px;
            opacity: 0.9;
            font-weight: 300;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 12px;
            font-size: 0.95em;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            opacity: 0.95;
        }

        .contact-item::before {
            content: '●';
            color: #e74c3c;
            font-size: 0.8em;
        }

        .content {
            padding: 45px 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 1.4em;
            margin-bottom: 20px;
            padding: 12px 0;
            border-bottom: 3px solid #e74c3c;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: #3498db;
        }

        .summary-text {
            color: #34495e;
            line-height: 1.8;
            text-align: justify;
            font-size: 1.05em;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        }

        .education-item {
            margin-bottom: 25px;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #e74c3c;
        }

        .education-item h3 {
            color: #2c3e50;
            font-size: 1.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .education-details {
            color: #7f8c8d;
            margin-bottom: 12px;
            line-height: 1.6;
        }

        .education-details strong {
            color: #2c3e50;
        }

        .skills-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 35px;
        }

        .skill-column {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #27ae60;
        }

        .skill-list {
            list-style: none;
            display: grid;
            gap: 10px;
        }

        .skill-list li {
            color: #34495e;
            padding: 8px 0;
            position: relative;
            padding-left: 20px;
            font-size: 1.05em;
        }

        .skill-list li::before {
            content: '▶';
            color: #e74c3c;
            position: absolute;
            left: 0;
            font-size: 0.8em;
        }

        .project-item {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #9b59b6;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .project-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .project-item h3 {
            color: #2c3e50;
            font-size: 1.15em;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .project-type {
            color: #e74c3c;
            font-style: italic;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .project-item p {
            color: #34495e;
            line-height: 1.7;
            text-align: justify;
            font-size: 1.05em;
        }

        .languages-section {
            display: flex;
            gap: 50px;
            flex-wrap: wrap;
        }

        .language-item {
            color: #34495e;
            font-size: 1.05em;
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 25px;
            border: 2px solid #3498db;
        }

        .language-item strong {
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 0;
            }

            .header {
                padding: 40px 20px;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .content {
                padding: 30px 20px;
            }

            .skills-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .languages-section {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiByeD0iNjAiIGZpbGw9IiNlNWU3ZWIiLz4KPHRleHQgeD0iNjAiIHk9IjY1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iU2Vnb2UgVUkiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM2YjcyODAiPlBob3RvPC90ZXh0Pgo8L3N2Zz4K" alt="Profile Photo" class="profile-photo">
            <div class="header-content">
                <h1>FADHL AMEEN HASAN ALI ALSHARIF</h1>
                <div class="subtitle">Artificial Intelligence Student</div>
                <div class="contact-info">
                    <div class="contact-item"><EMAIL></div>
                    <div class="contact-item">+90 ************</div>
                    <div class="contact-item">Expected Graduation: May 2026</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2>SUMMARY</h2>
                <div class="summary-text">
                    Motivated and detail-oriented Artificial Intelligence student with hands-on experience in machine learning, data analysis, and natural language processing. Proficient in programming languages like Python, C++, C# and SQL, and experienced in implementing AI algorithms using deep learning and NLP techniques. Passionate about solving real-world problems through innovative technology, and seeking opportunities to apply AI skills in a practical environment through summer training.
                </div>
            </div>

            <div class="section">
                <h2>EDUCATION</h2>
                <div class="education-item">
                    <h3>Cyprus International University</h3>
                    <div class="education-details">
                        • <strong>3rd year Bachelor of Artificial Intelligence engineering student</strong><br>
                        • <strong>Expected Graduation:</strong> [May, 2026]<br>
                        • <strong>Third-Year Student</strong> (2022 – Present)
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>SKILLS</h2>
                <div class="skills-grid">
                    <div>
                        <ul class="skill-list">
                            <li>NLP (Natural Language Processing)</li>
                            <li>Machine learning algorithm</li>
                            <li>Python (most commonly used)</li>
                            <li>C++</li>
                            <li>C#</li>
                            <li>Version Control: Git, GitHub</li>
                        </ul>
                    </div>
                    <div>
                        <ul class="skill-list">
                            <li>Pandas and NumPy for data manipulation</li>
                            <li>SQL for database queries</li>
                            <li>Excel</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>PROJECTS</h2>

                <div class="project-item">
                    <h3>Tweets Classification using LSTM (NLP Project)</h3>
                    <div class="project-type">Designed and managed a relational</div>
                    <p>Developed a deep learning model using (RNN) and (LSTM) to classify tweets as either natural disasters or unrelated content</p>
                </div>

                <div class="project-item">
                    <h3>Website with SQL Database Integration</h3>
                    <p>Developed a dynamic website and integrated it with a SQL database to manage and display data. Designed user interfaces for data input and retrieval, and implemented secure database queries to handle user requests</p>
                </div>

                <div class="project-item">
                    <h3>2D Car Game using C#</h3>
                    <p>Created a simple 2D car racing game as part of a visual programming course. Implemented game mechanics to increase or decrease car speed, detect pedestrians, and collect coins</p>
                </div>
            </div>

            <div class="section">
                <h2>COURSEES</h2>
                <div class="education-item">
                    <div class="education-details">
                        • Linear Algebra for Machine Learning ( Coursera )<br>
                        • Machine Learning for Engineers: Algorithms and Applications
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>LANGUAGES</h2>
                <div class="languages-section">
                    <div class="language-item">
                        <strong>Arabic</strong> (mother language)
                    </div>
                    <div class="language-item">
                        <strong>English</strong> (very good)
                    </div>
                    <div class="language-item">
                        <strong>Turkish</strong> (Good)
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>