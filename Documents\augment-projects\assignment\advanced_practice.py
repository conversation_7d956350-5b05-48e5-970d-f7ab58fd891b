# Advanced Python Practice - Level 2
# New challenges for Fadhl!

print("🚀 Welcome to Advanced Python Practice! 🚀")
print("Let's learn some cool new concepts!")

# ========== EXERCISE 1: Working with Files ==========
# Learn to save and read data from files
# Your code here:


# ========== EXERCISE 2: Classes and Objects ==========
# Create your own custom data types
# Example: Make a Person class with name, age, and a greeting method
# Your code here:


# ========== EXERCISE 3: List Comprehensions (Advanced) ==========
# Create lists in one line with conditions
# Example: Get all even numbers from 1 to 20
# Your code here:


# ========== EXERCISE 4: Working with Dates ==========
# Learn to work with dates and times
# Your code here:


# ========== EXERCISE 5: Simple Web Request ==========
# Learn to get data from the internet (if requests library is available)
# Your code here:


# ========== EXERCISE 6: Data Processing ==========
# Work with CSV data or JSON
# Your code here:


# ========== EXERCISE 7: Error Handling (Advanced) ==========
# Handle different types of errors gracefully
# Your code here:


# ========== EXERCISE 8: Decorators (Super Advanced!) ==========
# Functions that modify other functions
# Your code here:


# ========== EXERCISE 9: Lambda Functions ==========
# One-line functions for quick operations
# Your code here:


# ========== EXERCISE 10: Mini Project ==========
# Combine everything into a small useful program
# Ideas: Password generator, Simple calculator with history, 
#        Contact book, or To-do list
# Your code here:


print("🎉 Great job practicing advanced Python concepts! 🎉")

c= 1
x =19
y =5
f = x+y
print(f)
print(c)



current_Directory = os.getcwd()
print(f"The current working directory is: {current_directory}")


