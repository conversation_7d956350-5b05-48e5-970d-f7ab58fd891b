# Wedding Planning Scrum - Trello Board Setup Guide

## Board Structure

### Board Name: "Wedding Planning - Scrum MVP"

### Lists (Columns):
1. **Product Backlog** - All user stories prioritized
2. **Sprint Backlog** - Current sprint items
3. **In Progress** - Stories being worked on
4. **Review** - Completed stories awaiting approval
5. **Done** - Completed and approved stories
6. **Blocked** - Stories with impediments

## Team Setup

### Board Members:
- **<PERSON>** (Product Owner/Wedding Planner) - Admin
- **<PERSON>** (Scrum Master/Wedding Coordinator) - Admin  
- **Jessica** (Maid of Honor) - Member
- **David** (Best Man) - Member
- **Sarah's Mom** - Member
- **<PERSON>'s Dad** - Member

## Labels Configuration

### Priority Labels:
- 🔴 **High Priority** - Must have features
- 🟡 **Medium Priority** - Should have features  
- 🟢 **Low Priority** - Nice to have features

### Epic Labels:
- 🏛️ **Venue Selection** - All venue-related stories
- 👥 **Vendor Management** - Vendor booking and coordination
- 🎉 **Guest Experience** - Guest-related activities
- 💍 **Personal Preparation** - <PERSON><PERSON><PERSON>'s personal tasks

### Sprint Labels:
- 📅 **Sprint 1** - Venue Selection (Mar 1-14)
- 📅 **Sprint 2** - Key Vendors (Mar 15-28)
- 📅 **Sprint 3** - Guest Management (Mar 29-Apr 11)
- 📅 **Sprint 4** - Personal Prep (Apr 12-25)

## Card Template

### Card Title Format:
`[WP-XXX] Story Title`

### Card Description Template:
```
**User Story:**
As a [role], I want [functionality] so that [benefit]

**Acceptance Criteria:**
- [ ] Criterion 1
- [ ] Criterion 2  
- [ ] Criterion 3

**Story Points:** X
**Epic:** Epic Name
**Sprint:** Sprint X
**Assignee:** Team Member Name

**Definition of Done:**
- [ ] All acceptance criteria met
- [ ] Stakeholder approval received
- [ ] Budget impact documented
- [ ] Timeline updated
- [ ] Dependencies resolved
- [ ] Documentation completed
```

## Sample Cards Setup

### Card 1: Research Wedding Venues
**Title:** [WP-001] Research Wedding Venues
**List:** Product Backlog
**Labels:** High Priority, Venue Selection, Sprint 1
**Assigned:** Sarah Johnson
**Description:**
```
**User Story:**
As a couple, I want to research venues within 30 miles so that we can find options that fit our guest count and budget

**Acceptance Criteria:**
- [ ] List of 15+ venues with capacity, pricing, availability
- [ ] Venues categorized by style (outdoor, indoor, hybrid)
- [ ] Contact information and booking requirements documented

**Story Points:** 8
**Epic:** Venue Selection
**Sprint:** Sprint 1
**Assignee:** Sarah Johnson
```

### Card 2: Visit Top Venues
**Title:** [WP-002] Visit Top Venues
**List:** Product Backlog
**Labels:** High Priority, Venue Selection, Sprint 1
**Assigned:** Sarah & Michael Johnson
**Description:**
```
**User Story:**
As a bride, I want to visit top 5 venues in person so that I can visualize our ceremony there

**Acceptance Criteria:**
- [ ] Completed venue visit checklist for each location
- [ ] Photos taken of each venue setup
- [ ] Vendor restrictions and policies documented

**Story Points:** 13
**Epic:** Venue Selection
**Sprint:** Sprint 1
**Assignee:** Sarah & Michael Johnson
```

## Power-Ups to Enable

### Recommended Power-Ups:
1. **Calendar** - View sprint timelines and deadlines
2. **Voting** - Team prioritization of backlog items
3. **Time Tracking** - Monitor effort spent on stories
4. **Butler** - Automate card movements and notifications
5. **Custom Fields** - Add story points, epic tracking

### Custom Fields Setup:
- **Story Points** (Number field)
- **Epic** (Dropdown: Venue Selection, Vendor Management, Guest Experience, Personal Preparation)
- **Sprint** (Dropdown: Sprint 1, Sprint 2, Sprint 3, Sprint 4)
- **Original Estimate** (Number field)
- **Time Spent** (Number field)

## Automation Rules (Butler)

### Rule 1: Move to Sprint Backlog
**Trigger:** When a card is labeled "Sprint 1"
**Action:** Move card to "Sprint Backlog" list

### Rule 2: Notify on Blocked
**Trigger:** When a card is moved to "Blocked" list
**Action:** Add comment "@scrummaster Card blocked - needs attention"

### Rule 3: Auto-assign Sprint Cards
**Trigger:** When a card is moved to "In Progress"
**Action:** Set due date to end of current sprint

## Sprint Planning Process

### Before Sprint Planning:
1. Product Owner prioritizes Product Backlog
2. Team reviews capacity for upcoming sprint
3. Previous sprint retrospective actions reviewed

### During Sprint Planning:
1. Move selected stories from Product Backlog to Sprint Backlog
2. Add Sprint label to selected cards
3. Assign team members to specific cards
4. Break down large stories if needed
5. Confirm sprint goal in board description

### Daily Scrum Process:
1. Team reviews Sprint Backlog and In Progress columns
2. Update card positions based on progress
3. Add comments for impediments or blockers
4. Move blocked items to Blocked column

## Reporting and Metrics

### Sprint Burndown:
- Use Time Tracking power-up to monitor progress
- Create checklist in Sprint Goal card with daily updates
- Track story points completed vs. remaining

### Velocity Tracking:
- Record completed story points at end of each sprint
- Maintain running average for future sprint planning
- Document in board description or dedicated card

## Board Maintenance

### Weekly Tasks:
- Archive completed cards from Done column
- Update sprint progress in board description
- Review and prioritize Product Backlog
- Check for blocked items needing attention

### End of Sprint:
- Move incomplete items back to Product Backlog
- Archive sprint-specific labels
- Create new sprint labels for next iteration
- Update team velocity metrics

## Getting Started Checklist

- [ ] Create Trello board with specified lists
- [ ] Add all team members with appropriate permissions
- [ ] Configure labels for priorities, epics, and sprints
- [ ] Enable recommended power-ups
- [ ] Set up custom fields for story points and epics
- [ ] Create automation rules using Butler
- [ ] Import initial user stories as cards
- [ ] Schedule first sprint planning meeting
- [ ] Set up daily scrum meeting cadence
- [ ] Begin Sprint 1: Venue Selection

## Tips for Success

1. **Keep cards small** - Break down large stories into manageable tasks
2. **Update regularly** - Move cards as work progresses
3. **Use comments** - Document decisions and progress updates
4. **Review often** - Regular backlog grooming keeps priorities current
5. **Celebrate wins** - Acknowledge completed sprints and milestones

This Trello setup will provide a visual, collaborative workspace for your wedding planning Scrum project, making it easy to track progress and maintain team alignment throughout the planning process.
