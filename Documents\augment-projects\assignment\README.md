# Operating System Multiple Queue Scheduler Simulator

## Project Overview
This project implements a CPU scheduling simulator that demonstrates three different scheduling algorithms:
- First Come First Serve (FCFS)
- Shortest Job First (SJF) - Non-preemptive
- Priority Scheduling - Non-preemptive

## Author Information
- **Course**: CPE 351 - Operating Systems
- **Assignment**: Multiple Queue Scheduler Simulator
- **Date**: July 25, 2025

## Compilation Instructions

### On GNU/Linux Systems:
```bash
# Using the provided Makefile
make

# Or compile directly with g++
g++ -std=c++11 -Wall -Wextra -O2 -o cpe351 scheduler.cpp
```

### On Windows (with MinGW or similar):
```cmd
g++ -std=c++11 -Wall -Wextra -O2 -o cpe351.exe scheduler.cpp
```

## Usage
```bash
./cpe351 input_file.txt output_file.txt
```

## Input File Format
The input file should contain process data in the following format:
```
BurstTime:Priority:QueueID
```

Example:
```
10:1:0
5:0:0
3:2:0
2:1:0
3:1:1
10:0:1
2:2:1
```

## Output Format
The program outputs results in the following format:
```
QueueID:Algorithm:WT1:WT2:...:WTn:AWT
```

Where:
- **QueueID**: The queue identifier
- **Algorithm**: 1=FCFS, 2=SJF, 3=Priority
- **WT1, WT2, ...**: Waiting times for each process
- **AWT**: Average waiting time

## Algorithm Descriptions

### 1. First Come First Serve (FCFS)
- Processes are executed in the order they appear in the input file
- Simple and fair, but may lead to convoy effect
- Waiting time calculation: Each process waits for all previous processes to complete

### 2. Shortest Job First (SJF)
- Processes are sorted by burst time (shortest first)
- Minimizes average waiting time
- Non-preemptive version implemented

### 3. Priority Scheduling
- Processes are sorted by priority (lower number = higher priority)
- Higher priority processes execute first
- Non-preemptive version implemented

## Project Requirements Compliance
- ✅ Written in C++ and compiles on GNU/Linux
- ✅ No arrays used (vectors and individual variables only)
- ✅ No special libraries (only standard C++ libraries)
- ✅ Custom implementations for all algorithms
- ✅ Proper file I/O with colon-delimited format
- ✅ Multiple queue support

## Testing
Use the provided `input.txt` file to test the program:
```bash
./cpe351 input.txt output.txt
```

The program will display results on screen and write them to the output file.

## Code Structure
- **Process struct**: Represents individual processes with burst time, priority, and queue ID
- **SchedulerSimulator class**: Main class handling file I/O and algorithm implementations
- **Algorithm functions**: Separate functions for each scheduling algorithm
- **Helper functions**: Utility functions for calculations and data management

## Academic Integrity
This code was written as original work for the CPE 351 course assignment. All implementations are custom-written without copying from external sources.
