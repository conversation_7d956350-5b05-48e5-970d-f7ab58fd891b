Project Management Assignment: Planning Your Dream Wedding Using Scrum Methodology

Assignment Overview

This assignment challenges you to apply Scrum project management principles to plan a wedding celebration. You'll experience firsthand how agile methodologies can transform complex personal projects into manageable, iterative processes.

Duration: 4 weeks
Deliverables: Complete Scrum artifacts and reflection report
Learning Objectives: Master Scrum framework through real-world application

Background Context

Wedding planning represents one of life's most complex personal projects. With multiple stakeholders, tight deadlines, budget constraints, and countless moving parts, it mirrors the challenges faced in professional software development environments. This assignment demonstrates how Scrum's iterative approach can bring structure and clarity to seemingly overwhelming projects.

Recent industry research shows that couples who use structured project management approaches report 40% less stress during their planning process and achieve better budget adherence compared to traditional planning methods.

Part 1: Project Initiation & Vision Creation

Task 1.1: Establish Your Wedding Vision
Create a comprehensive vision statement that answers:
• What type of celebration reflects your personality?
• Who are your key stakeholders (parents, wedding party, vendors)?
• What constraints exist (budget, timeline, venue availability)?
• What defines success for this project?

Deliverable: One-page vision document with stakeholder analysis

Task 1.2: Form Your Scrum Team
Identify and assign the following roles:
• Product Owner: Makes final decisions, manages budget, prioritizes features
• Scrum Master: Facilitates ceremonies, removes obstacles, maintains process
• Development Team: Friends/family who will execute tasks

Deliverable: Team charter with role definitions and contact information

Part 2: Scrum Artifacts Development

Task 2.1: Create the Product Backlog
Develop a comprehensive list of all wedding-related tasks. Consider these categories:

Venue & Logistics:
• Research potential venues
• Schedule site visits
• Negotiate contracts
• Coordinate transportation

Guest Experience:
• Compile guest list
• Design invitations
• Plan ceremony flow
• Organize reception activities

Vendor Management:
• Interview photographers
• Select catering options
• Choose floral arrangements
• Book entertainment

Personal Preparation:
• Select wedding attire
• Plan honeymoon
• Arrange beauty appointments
• Prepare vows

Deliverable: Prioritized product backlog with 50+ user stories

Task 2.2: Write User Stories
Transform backlog items into proper user stories using the format:
"As a [role], I want [functionality] so that [benefit]"

Examples:
• "As a bride, I want to taste-test three catering options so that I can select the perfect menu for our guests"
• "As a groom, I want to research photographers' portfolios so that we capture our special moments beautifully"

Deliverable: 20 detailed user stories with acceptance criteria

Part 3: Sprint Planning & Execution

Task 3.1: Plan Your First Sprint
Design a 2-week sprint focusing on venue selection:

Sprint Goal: "Secure the perfect venue that aligns with our vision and budget"

Sprint Backlog Items:
• Research 10 potential venues
• Schedule 5 venue visits
• Compare pricing and availability
• Make final venue decision
• Sign venue contract

Deliverable: Sprint planning document with task breakdown and time estimates

Task 3.2: Daily Scrum Simulation
Document 5 consecutive daily scrum meetings showing:
• What did you accomplish yesterday?
• What will you work on today?
• What obstacles are blocking progress?

Deliverable: Daily scrum log with impediment tracking

Task 3.3: Sprint Review & Retrospective
After completing your sprint:

Sprint Review:
• Demonstrate completed work to stakeholders
• Gather feedback on venue selection
• Update product backlog based on new insights

Sprint Retrospective:
• What went well during the sprint?
• What could be improved?
• What actions will you take in the next sprint?

Deliverable: Sprint review presentation and retrospective action items

Part 4: Advanced Scrum Concepts

Task 4.1: Velocity Tracking
Calculate your team's velocity by tracking:
• Story points completed per sprint
• Actual vs. estimated effort
• Factors affecting team performance

Deliverable: Velocity chart with trend analysis

Task 4.2: Risk Management
Identify potential risks and mitigation strategies:
• Weather contingencies for outdoor ceremonies
• Vendor cancellations
• Budget overruns
• Timeline delays

Deliverable: Risk register with probability/impact matrix

Part 5: Reflection & Analysis

Task 5.1: Methodology Comparison
Compare your Scrum experience with traditional wedding planning approaches:
• How did iterative planning affect stress levels?
• What advantages did regular stakeholder feedback provide?
• Where did the Scrum framework feel most/least natural?

Task 5.2: Professional Application
Analyze how this experience translates to workplace projects:
• Which Scrum principles proved most valuable?
• How would you adapt this approach for software development?
• What challenges might arise when scaling to larger teams?

Deliverable: 1500-word reflection essay with specific examples

Grading Rubric

Component | Excellent (90-100%) | Good (80-89%) | Satisfactory (70-79%) | Needs Improvement (<70%)
Vision & Planning | Clear, comprehensive vision with detailed stakeholder analysis | Good vision with minor gaps in stakeholder consideration | Basic vision with limited stakeholder input | Unclear or incomplete vision
Scrum Artifacts | All artifacts complete, well-structured, and professionally presented | Most artifacts complete with good quality | Basic artifacts with some missing elements | Incomplete or poorly structured artifacts
Process Execution | Demonstrates deep understanding of Scrum ceremonies and principles | Shows good grasp of Scrum with minor process deviations | Basic understanding with some confusion about roles/ceremonies | Limited understanding of Scrum framework
Reflection Quality | Insightful analysis connecting personal experience to professional applications | Good reflection with some meaningful connections | Basic reflection with limited depth | Superficial or incomplete reflection

Submission Requirements

1. Digital Portfolio: Create a shared workspace (Trello, Jira, or similar) containing all Scrum artifacts
2. Documentation Package: Submit all deliverables in a organized folder structure
3. Presentation: 10-minute presentation showcasing your Scrum journey and key learnings

Due Date: [Insert specific date - 4 weeks from assignment start]

Additional Resources

• Scrum Guide (official): https://scrumguides.org/
• Wedding planning templates and tools
• Project management software tutorials
• Agile estimation techniques guide

Academic Integrity Note

This assignment requires original work based on your personal wedding planning scenario (real or hypothetical). While you may collaborate with classmates on understanding Scrum concepts, all deliverables must represent your individual effort and unique project context.

Remember: The goal isn't to plan an actual wedding, but to demonstrate mastery of Scrum methodology through this engaging, relatable project framework.
