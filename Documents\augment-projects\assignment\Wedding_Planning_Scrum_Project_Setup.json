{"project": {"name": "Wedding Planning Using Scrum Methodology", "description": "MVP wedding planning project using Scrum framework", "type": "Scrum", "duration": "8 months", "budget": "$25,000"}, "personnel": {"product_owner": {"role": "Product Owner (Wedding Planner)", "name": "<PERSON>", "responsibilities": ["Prioritizing wedding features and managing budget", "Making final decisions on vendor selections and design choices", "Maintaining vision alignment throughout planning process", "Defining requirements and acceptance criteria for all wedding elements"]}, "scrum_master": {"role": "<PERSON><PERSON> (Wedding Coordinator)", "name": "<PERSON>", "responsibilities": ["Facilitating all planning meetings and daily check-ins", "Removing obstacles and resolving conflicts between stakeholders", "Ensuring team stays focused on sprint goals and timelines", "Coordinating communication between all team members and vendors"]}, "development_team": {"role": "Development Team (Wedding Team)", "members": [{"name": "<PERSON> (Maid of Honor)", "focus": "Vendor research, décor planning, day-of coordination"}, {"name": "<PERSON> (Best Man)", "focus": "Music coordination, transportation, groomsmen management"}, {"name": "<PERSON>'s Mom", "focus": "Guest management, traditional elements, family coordination"}, {"name": "<PERSON>'s Dad", "focus": "Venue logistics, vendor negotiations, technical setup"}]}}, "epics": [{"id": "EPIC-001", "name": "Venue Selection and Setup", "description": "Secure and prepare the perfect venue for ceremony and reception", "priority": "High", "story_points": 50}, {"id": "EPIC-002", "name": "Vendor Management", "description": "Book and coordinate all essential wedding vendors", "priority": "High", "story_points": 45}, {"id": "EPIC-003", "name": "Guest Experience", "description": "Manage guest communications and experience planning", "priority": "Medium", "story_points": 35}, {"id": "EPIC-004", "name": "Personal Preparation", "description": "<PERSON>le couple's personal wedding preparations", "priority": "Medium", "story_points": 30}], "user_stories": [{"id": "WP-001", "epic": "EPIC-001", "title": "Research Wedding Venues", "description": "As a couple, I want to research venues within 30 miles so that we can find options that fit our guest count and budget", "acceptance_criteria": ["List of 15+ venues with capacity, pricing, availability", "Venues categorized by style (outdoor, indoor, hybrid)", "Contact information and booking requirements documented"], "story_points": 8, "priority": "High", "status": "To Do"}, {"id": "WP-002", "epic": "EPIC-001", "title": "Visit Top Venues", "description": "As a bride, I want to visit top 5 venues in person so that I can visualize our ceremony there", "acceptance_criteria": ["Completed venue visit checklist for each location", "Photos taken of each venue setup", "Vendor restrictions and policies documented"], "story_points": 13, "priority": "High", "status": "To Do"}, {"id": "WP-003", "epic": "EPIC-001", "title": "Negotiate Venue Contract", "description": "As a groom, I want to negotiate contract terms so that we get the best value for our budget", "acceptance_criteria": ["Signed contract with favorable terms", "Cancellation policy clearly understood", "Payment schedule agreed upon"], "story_points": 5, "priority": "High", "status": "To Do"}, {"id": "WP-004", "epic": "EPIC-002", "title": "Book Wedding Photographer", "description": "As a couple, I want to review photographer portfolios and book our photographer so that we capture our special day beautifully", "acceptance_criteria": ["Review 15+ photographer portfolios", "Meet with top 3 finalists", "Check references from recent clients", "Sign contract with chosen photographer"], "story_points": 8, "priority": "High", "status": "To Do"}, {"id": "WP-005", "epic": "EPIC-002", "title": "Select Wedding Catering", "description": "As a couple, I want to taste-test menu options from different caterers so that we provide delicious food for our guests", "acceptance_criteria": ["Research 8+ catering companies", "Schedule tastings with top 4 caterers", "Accommodate dietary restrictions", "Finalize menu and service style"], "story_points": 10, "priority": "High", "status": "To Do"}, {"id": "WP-006", "epic": "EPIC-002", "title": "Choose Floral Arrangements", "description": "As a bride, I want to design floral arrangements so that the venue looks beautiful and matches our theme", "acceptance_criteria": ["Meet with 3 florists", "Design ceremony and reception arrangements", "Select bridal bouquet and boutonnieres", "Confirm delivery and setup timeline"], "story_points": 6, "priority": "Medium", "status": "To Do"}, {"id": "WP-007", "epic": "EPIC-003", "title": "Create Guest List", "description": "As a couple, I want to create a master guest list so that we know exactly who to invite", "acceptance_criteria": ["Categorized list with contact information", "RSVP tracking system setup", "Dietary restrictions noted", "Plus-one policies defined"], "story_points": 5, "priority": "High", "status": "To Do"}, {"id": "WP-008", "epic": "EPIC-003", "title": "Design Wedding Invitations", "description": "As a bride, I want to design beautiful invitations so that guests are excited about our wedding", "acceptance_criteria": ["Design approved by both partners", "All wedding details included", "RSVP cards and envelopes prepared", "Printing and mailing timeline set"], "story_points": 4, "priority": "Medium", "status": "To Do"}, {"id": "WP-009", "epic": "EPIC-004", "title": "Select Wedding Attire", "description": "As a couple, I want to choose our wedding attire so that we look and feel amazing on our special day", "acceptance_criteria": ["Wedding dress selected and fitted", "G<PERSON>'s suit/tuxedo chosen", "Accessories and shoes coordinated", "Backup outfit options considered"], "story_points": 7, "priority": "Medium", "status": "To Do"}, {"id": "WP-010", "epic": "EPIC-004", "title": "Plan Honeymoon", "description": "As a couple, I want to plan our honeymoon so that we can relax and celebrate after the wedding", "acceptance_criteria": ["Destination researched and booked", "Travel arrangements confirmed", "Activities and accommodations planned", "Budget allocated and managed"], "story_points": 6, "priority": "Low", "status": "To Do"}], "sprints": [{"id": "SPRINT-001", "name": "Venue Selection Sprint", "duration": "2 weeks", "start_date": "2024-03-01", "end_date": "2024-03-14", "goal": "Secure the perfect venue that aligns with our vision and budget", "stories": ["WP-001", "WP-002", "WP-003"], "capacity": 34, "status": "Planned"}, {"id": "SPRINT-002", "name": "Key Vendor Selection Sprint", "duration": "2 weeks", "start_date": "2024-03-15", "end_date": "2024-03-28", "goal": "Book photographer, caterer, and florist for wedding day", "stories": ["WP-004", "WP-005", "WP-006"], "capacity": 32, "status": "Planned"}, {"id": "SPRINT-003", "name": "Guest Management Sprint", "duration": "2 weeks", "start_date": "2024-03-29", "end_date": "2024-04-11", "goal": "Finalize guest list and send invitations", "stories": ["WP-007", "WP-008"], "capacity": 28, "status": "Planned"}, {"id": "SPRINT-004", "name": "Personal Preparation Sprint", "duration": "2 weeks", "start_date": "2024-04-12", "end_date": "2024-04-25", "goal": "Complete personal wedding preparations", "stories": ["WP-009", "WP-010"], "capacity": 30, "status": "Planned"}], "ceremonies": {"sprint_planning": {"frequency": "Every 2 weeks", "duration": "2 hours", "participants": ["Product Owner", "Scrum Master", "Development Team"]}, "daily_scrum": {"frequency": "Daily", "duration": "15 minutes", "time": "7:00 PM", "participants": ["Scrum Master", "Development Team"]}, "sprint_review": {"frequency": "End of each sprint", "duration": "1 hour", "participants": ["Product Owner", "Scrum Master", "Development Team", "Stakeholders"]}, "sprint_retrospective": {"frequency": "End of each sprint", "duration": "45 minutes", "participants": ["Product Owner", "Scrum Master", "Development Team"]}}, "definition_of_done": ["All acceptance criteria met", "Stakeholder approval received", "Budget impact documented", "Timeline updated", "Dependencies resolved", "Documentation completed"]}