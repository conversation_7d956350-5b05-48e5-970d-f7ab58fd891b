# Welcome to Python Basics!
# This file will teach you the fundamentals of Python programming

# 1. COMMENTS
# Lines starting with # are comments - they don't run as code
# Use comments to explain what your code does

print("Hello, World!")  # This prints text to the screen

# 2. VARIABLES
# Variables store data that you can use later

name = "fadhl"
age = 24
height = 55.1
is_student = True
print ("name", name)
print ("age",age)
print("height", height)
print("is student", is_student)
 

# 3. DATA TYPES
# Python has several basic data types:
# - str (string): text like "hello"
# - int (integer): whole numbers like 42
# - float: decimal numbers like 3.14
# - bool (boolean): True or False

# 4. BASIC MATH
x = 10
y = 3

print("Addition:", x + y)      # 13
print("Subtraction:", x - y)   # 7
print("Multiplication:", x * y) # 30
print("Division:", x / y)      # 3.333...
print("Integer division:", x // y) # 3
print("Remainder:", x % y)     # 1
print("Power:", ximport os

# Get the current working directory
current_directory = os.getcwd()
print(f"The current working directory is: {current_directory}")

# Define the filename
file_name = "abu_ameen.py"

# Join the directory and filename to create the full path
full_path = os.path.join(current_directory, file_name)
print(f"The full path to the file is: {full_path}")

# You can also check if the file actually exists at that path
if os.path.exists(full_path):
    print(f"\nSuccess! The file '{file_name}' was found.")
else:
    print(f"\nNote: The file '{file_name}' does not exist in this directory yet.")

 ** y)        # 1000

# 5. STRINGS
first_name = "John"
last_name = "Doe"
full_name = first_name + " " + last_name  # String concatenation
print("Full name:", full_name)

# String methods
message = "Hello Python"
print("Uppercase:", message.upper())
print("Lowercase:", message.lower())
print("Length:", len(message))

# 6. GETTING USER INPUT
# Uncomment the line below to try getting input from user
# user_name = input("What's your name? ")
# print("Nice to meet you,", user_name)

# 7. LISTS
# Lists store multiple items
fruits = ["apple", "banana", "orange"]
numbers = [1, 2, 3, 4, 5]

print("Fruits:", fruits)
print("First fruit:", fruits[0])  # Lists start at index 0
print("Last fruit:", fruits[-1])  # -1 gets the last item

# Adding to lists
fruits.append("grape")
print("After adding grape:", fruits)

# 8. BASIC IF STATEMENTS
temperature = 75

if temperature > 80:
    print("It's hot outside!")
elif temperature > 60:
    print("It's nice weather!")
else:
    print("It's cold outside!")

# 9. LOOPS
# For loop - repeat for each item
print("\nCounting to 5:")
for i in range(1, 6):
    print(i)

print("\nFruits list:")
for fruit in fruits:
    print("I like", fruit)

# While loop - repeat while condition is true
count = 0
print("\nWhile loop:")
while count < 3:
    print("Count is", count)
    count = count + 1

# 10. FUNCTIONS
def greet(name):
    """This function greets someone"""
    return "Hello, " + name + "!"

def add_numbers(a, b):
    """This function adds two numbers"""
    return a + b

# Using functions
greeting = greet("Python learner")
print(greeting)

result = add_numbers(5, 3)
print("5 + 3 =", result)

# Practice exercises for you to try:
print("\n" + "="*50)
print("PRACTICE EXERCISES:")
print("="*50)
print("1. Create a variable with your favorite color")
print("2. Make a list of your hobbies")
print("3. Write a function that multiplies two numbers")
print("4. Use an if statement to check if a number is even or odd")
print("5. Create a loop that prints numbers from 1 to 10")
