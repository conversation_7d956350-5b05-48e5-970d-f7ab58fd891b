# SDLC and Agile Methodology - Questions

## Multiple Choice Questions (20)

**1. What does SDLC stand for?**
a) Software Development Life Cycle
b) System Design Life Cycle
c) Software Design Life Course
d) System Development Life Course

**2. What is the correct sequence of phases in the traditional SDLC?**
a) Design → Requirement → Coding → Testing
b) Requirement → Design → Coding → Testing
c) Coding → Design → Requirement → Testing
d) Testing → Coding → Design → Requirement

**3. In the Waterfall model, what happens if you want to change something?**
a) You can easily modify any phase
b) You must start from Step 1
c) You only need to modify the current step
d) Changes are not allowed

**4. Can the Waterfall model be used in modern software development?**
a) No, it's completely obsolete
b) Yes, if the requirements don't change
c) Only for small projects
d) Only for web development

**5. Which of the following is an example mentioned for stable requirements?**
a) Social media apps
b) Traffic lights and airlines
c) Mobile games
d) E-commerce websites

**6. How many team operations are mentioned in Agile?**
a) 1
b) 2
c) 3
d) 4

**7. Which of the following is NOT mentioned as an Agile methodology?**
a) Scrum
b) Kanban
c) XP
d) Waterfall

**8. What percentage is associated with <PERSON><PERSON> in the notes?**
a) 70%
b) 80%
c) 90%
d) 100%

**9. What does "Product Backlog" contain?**
a) Completed features
b) All collected features
c) Only important features
d) Testing results

**10. What does "Sprint BL" refer to?**
a) Sprint Baseline
b) Sprint Backlog with important features
c) Sprint Build List
d) Sprint Bug Log

**11. How many sprints are shown in the diagram?**
a) 2
b) 3
c) 4
d) 5

**12. What is the cycle within each sprint?**
a) Req → Des → Build → Test
b) Plan → Code → Test → Deploy
c) Design → Code → Test → Review
d) Requirement → Code → Build → Release

**13. What happens in Sprint 1 according to the notes?**
a) All features are completed
b) The customer is shown progress
c) Testing is finalized
d) Requirements are gathered

**14. When are updates implemented in the Agile process?**
a) Only at the end of all sprints
b) In Sprint 2 if there are customer updates
c) Never during the process
d) Only in Sprint 1

**15. What is the main characteristic of Agile work?**
a) Individual work
b) Team work
c) Management work
d) Client work

**16. What type of product should Agile focus on according to the notes?**
a) Complex product
b) Simple product
c) Minimal Viable Product
d) Complete product

**17. What is mentioned as an example requirement?**
a) User login
b) Sign up and payment
c) Database design
d) User interface

**18. In the Waterfall model, what is the main problem mentioned?**
a) It's too fast
b) It's too expensive
c) If you want to change anything, you start from Step 1
d) It requires too many people

**19. What makes Agile different from Waterfall in terms of changes?**
a) Agile doesn't allow changes
b) Agile accommodates changes between sprints
c) Agile is slower with changes
d) Agile requires more documentation for changes

**20. What is the main advantage of showing progress to customers in Sprint 1?**
a) To get final approval
b) To collect feedback for updates in Sprint 2
c) To end the project early
d) To reduce costs

## Classic Questions (10)

**1. Explain the fundamental differences between the Waterfall model and Agile methodology in software development. Provide specific examples of when each approach would be most appropriate.**

**2. Describe the complete Agile sprint cycle and explain how customer feedback is incorporated into the development process.**

**3. Analyze the advantages and disadvantages of the Waterfall model. Why might some projects still use this approach despite its limitations?**

**4. What is a Minimal Viable Product (MVP) in Agile development, and how does it relate to the concept of Sprint Backlog and Product Backlog?**

**5. Compare and contrast Scrum, Kanban, and XP methodologies. What are the key characteristics that distinguish each approach?**

**6. Explain the role of team operations in Agile development. How does teamwork contribute to the success of Agile projects?**

**7. Discuss the importance of requirement stability in choosing between Waterfall and Agile methodologies. Provide real-world examples to support your answer.**

**8. Describe the sprint planning process and explain how features move from the Product Backlog to Sprint Backlog. What criteria should be used for prioritization?**

**9. Evaluate the statement "Agile is team work." How does this collaborative approach impact project outcomes compared to traditional development methods?**

**10. Analyze the iterative nature of Agile development. How does the cycle of Requirement → Design → Build → Test within each sprint contribute to overall project success?**
