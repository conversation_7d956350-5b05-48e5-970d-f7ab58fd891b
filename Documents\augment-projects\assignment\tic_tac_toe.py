# Tic-Tac-Toe Game
# A fun project to practice Python fundamentals!

print("🎮 Welcome to Tic-Tac-Toe! 🎮")
print("Players will take turns placing X and O on a 3x3 grid")
print("First to get 3 in a row (horizontal, vertical, or diagonal) wins!")
print("-" * 50)

# ========== STEP 1: Create the Game Board ==========
# TODO: Create a function to initialize an empty 3x3 board
# Hint: You can use a list with 9 elements, numbered 1-9 initially
# Example: board = ['1', '2', '3', '4', '5', '6', '7', '8', '9']

def create_board():
    """
    Creates and returns a new game board with positions 1-9
    This makes it easy for players to see where they can move
    """
    # Your code here:
    pass


# ========== STEP 2: Display the Board ==========
# TODO: Create a function to display the current board state
# Make it look nice with lines and formatting!

def display_board(board):
    """
    Displays the current state of the board in a nice 3x3 format
    Should look something like:
     1 | 2 | 3 
    -----------
     4 | 5 | 6 
    -----------
     7 | 8 | 9 
    """
    # Your code here:
    # Hint: Use print() with string formatting
    # You'll need to access board[0], board[1], board[2] for first row, etc.
    pass


# ========== STEP 3: Get Player Move ==========
# TODO: Create a function to get and validate player input

def get_player_move(player):
    """
    Gets a valid move from the current player
    Should ask for input and validate it (1-9, position not taken)
    Returns the position as a string
    """
    # Your code here:
    # Hint: Use input() to get player choice
    # Hint: Use a while loop to keep asking until valid input
    # Hint: Check if the input is a number between 1-9
    pass


# ========== STEP 4: Make a Move ==========
# TODO: Create a function to update the board with player's move

def make_move(board, position, player):
    """
    Updates the board with the player's move
    position: where to place the mark (1-9)
    player: 'X' or 'O'
    """
    # Your code here:
    # Hint: Convert position to board index (position 1 = board[0])
    # Hint: Replace the number with the player's symbol
    pass


# ========== STEP 5: Check for Winner ==========
# TODO: Create a function to check if someone won

def check_winner(board):
    """
    Checks if there's a winner on the board
    Returns 'X' if X wins, 'O' if O wins, or None if no winner
    
    Win conditions to check:
    - Rows: [0,1,2], [3,4,5], [6,7,8]
    - Columns: [0,3,6], [1,4,7], [2,5,8]  
    - Diagonals: [0,4,8], [2,4,6]
    """
    # Your code here:
    # Hint: Create a list of all winning combinations
    # Hint: Check each combination to see if all 3 positions have same symbol
    pass


# ========== STEP 6: Check if Board is Full ==========
# TODO: Create a function to check for tie game

def is_board_full(board):
    """
    Checks if the board is full (tie game)
    Returns True if no more moves possible, False otherwise
    """
    # Your code here:
    # Hint: Check if any positions still contain numbers 1-9
    pass


# ========== STEP 7: Main Game Loop ==========
# TODO: Create the main game function that ties everything together

def play_game():
    """
    Main game loop that runs the entire tic-tac-toe game
    """
    # Your code here:
    # Game flow:
    # 1. Create new board
    # 2. Set current player to 'X'
    # 3. Start game loop:
    #    - Display board
    #    - Get player move
    #    - Make the move
    #    - Check for winner
    #    - Check for tie
    #    - Switch players
    # 4. Display final result
    
    print("Starting new game...")
    # Initialize your variables here
    
    # Main game loop
    while True:
        # Your game logic here
        pass


# ========== STEP 8: Run the Game ==========
# TODO: Add code to start the game and ask if player wants to play again

if __name__ == "__main__":
    """
    This runs when the file is executed directly
    """
    # Your code here:
    # Hint: You might want a loop to allow multiple games
    # Hint: Ask player if they want to play again after each game
    pass


# ========== BONUS CHALLENGES (Optional) ==========
# Once you get the basic game working, try these enhancements:

# 1. Add input validation to handle invalid entries gracefully
# 2. Add a score counter for multiple games
# 3. Add an AI opponent that makes random moves
# 4. Add difficulty levels for AI (easy/hard)
# 5. Add colors to make X and O stand out
# 6. Save game statistics to a file

print("🎯 Ready to code your tic-tac-toe game! 🎯")
print("Start with Step 1 and work your way through each function.")
print("Test each function as you write it!")
