"""
Main Application Runner
Course Equivalency Analysis System for CIU, EMU, and Near East University
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from data_scraper import UniversityCourseScraper
from course_matcher import CourseEquivalencyMatcher
from sample_data import save_sample_data
import pandas as pd

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CourseEquivalencySystem:
    def __init__(self):
        self.scraper = UniversityCourseScraper()
        self.matcher = CourseEquivalencyMatcher()
        
    def setup_environment(self):
        """Setup the environment and install dependencies"""
        logger.info("Setting up environment...")
        
        # Check if required files exist
        required_files = ['requirements.txt']
        for file in required_files:
            if not os.path.exists(file):
                logger.error(f"Required file {file} not found!")
                return False
        
        logger.info("Environment setup complete!")
        return True
    
    def generate_sample_data(self):
        """Generate sample course data for testing"""
        logger.info("Generating sample course data...")
        try:
            df = save_sample_data()
            logger.info(f"Generated {len(df)} sample courses")
            return True
        except Exception as e:
            logger.error(f"Failed to generate sample data: {e}")
            return False
    
    def scrape_real_data(self):
        """Scrape real course data from university websites"""
        logger.info("Scraping real course data from universities...")
        try:
            courses = self.scraper.scrape_all_universities()
            df = self.scraper.save_to_csv(courses)
            self.scraper.save_to_json(courses)
            logger.info(f"Successfully scraped {len(courses)} courses")
            return True
        except Exception as e:
            logger.error(f"Failed to scrape real data: {e}")
            return False
        finally:
            self.scraper.close()
    
    def analyze_courses(self, similarity_threshold=0.6):
        """Analyze course equivalencies"""
        logger.info("Analyzing course equivalencies...")
        
        # Load course data
        if not os.path.exists('university_courses.csv'):
            logger.error("No course data found. Please generate sample data or scrape real data first.")
            return False
        
        try:
            courses_df = pd.read_csv('university_courses.csv')
            logger.info(f"Loaded {len(courses_df)} courses")
            
            # Find matches
            matches_df = self.matcher.find_course_matches(courses_df, min_similarity=similarity_threshold)
            
            # Save results
            self.matcher.save_matches(matches_df)
            self.matcher.save_matches_json(matches_df)
            
            logger.info(f"Found {len(matches_df)} course matches")
            
            # Display summary
            self.display_analysis_summary(courses_df, matches_df)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to analyze courses: {e}")
            return False
    
    def display_analysis_summary(self, courses_df, matches_df):
        """Display analysis summary"""
        print("\n" + "="*60)
        print("COURSE EQUIVALENCY ANALYSIS SUMMARY")
        print("="*60)
        
        print(f"\n📊 COURSE STATISTICS:")
        print(f"   Total Courses: {len(courses_df)}")
        print(f"   Universities: {courses_df['university'].nunique()}")
        print(f"   Programs: {courses_df['program'].nunique()}")
        
        print(f"\n🔗 MATCH STATISTICS:")
        print(f"   Total Matches: {len(matches_df)}")
        
        if not matches_df.empty:
            print(f"\n📈 CONFIDENCE DISTRIBUTION:")
            confidence_counts = matches_df['confidence'].value_counts()
            for confidence, count in confidence_counts.items():
                print(f"   {confidence}: {count} matches")
            
            print(f"\n🎯 TOP MATCHES:")
            top_matches = matches_df.nlargest(5, 'overall_similarity')
            for _, match in top_matches.iterrows():
                print(f"   {match['course1_code']} ({match['university1']}) ↔ "
                      f"{match['course2_code']} ({match['university2']}) - "
                      f"{match['overall_similarity']:.1%} similarity")
        
        print("\n" + "="*60)
    
    def generate_transfer_plan(self, source_uni, target_uni, course_codes):
        """Generate a transfer plan for specific courses"""
        logger.info(f"Generating transfer plan from {source_uni} to {target_uni}")
        
        if not os.path.exists('course_matches.csv'):
            logger.error("No match data found. Please run analysis first.")
            return False
        
        try:
            matches_df = pd.read_csv('course_matches.csv')
            
            # Convert course_codes string to list if needed
            if isinstance(course_codes, str):
                course_codes = [code.strip() for code in course_codes.split(',')]
            
            transfer_plan = self.matcher.generate_transfer_plan(
                matches_df, source_uni, target_uni, course_codes
            )
            
            self.display_transfer_plan(transfer_plan, source_uni, target_uni)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to generate transfer plan: {e}")
            return False
    
    def display_transfer_plan(self, transfer_plan, source_uni, target_uni):
        """Display transfer plan results"""
        print(f"\n🎯 TRANSFER PLAN: {source_uni} → {target_uni}")
        print("="*60)
        
        summary = transfer_plan['summary']
        print(f"\n📊 SUMMARY:")
        print(f"   Total Courses: {summary['total_courses']}")
        print(f"   Transferable: {summary['transferable_courses']}")
        print(f"   Original Credits: {summary['original_credits']}")
        print(f"   Transferred Credits: {summary['transferred_credits']}")
        print(f"   Credit Loss: {summary['credit_loss']}")
        
        if transfer_plan['transfer_plan']:
            print(f"\n📋 DETAILED TRANSFER PLAN:")
            for i, course in enumerate(transfer_plan['transfer_plan'], 1):
                print(f"\n   {i}. {course['original_course']} → {course['transfer_to']}")
                print(f"      Credits: {course['original_credits']} → {course['transfer_credits']}")
                print(f"      Similarity: {course['similarity']:.1%}")
                print(f"      Confidence: {course['confidence']}")
                print(f"      Recommendation: {course['recommendation']}")
        
        print("\n" + "="*60)
    
    def run_dashboard(self):
        """Launch the web dashboard"""
        logger.info("Launching web dashboard...")
        try:
            import subprocess
            subprocess.run([sys.executable, "-m", "streamlit", "run", "dashboard.py"])
        except Exception as e:
            logger.error(f"Failed to launch dashboard: {e}")
            print("\nTo manually launch the dashboard, run:")
            print("streamlit run dashboard.py")

def main():
    parser = argparse.ArgumentParser(description='University Course Equivalency Analysis System')
    parser.add_argument('--mode', choices=['sample', 'scrape', 'analyze', 'transfer', 'dashboard'], 
                       default='sample', help='Operation mode')
    parser.add_argument('--threshold', type=float, default=0.6, 
                       help='Similarity threshold for analysis (0.5-1.0)')
    parser.add_argument('--source', type=str, help='Source university for transfer plan')
    parser.add_argument('--target', type=str, help='Target university for transfer plan')
    parser.add_argument('--courses', type=str, help='Comma-separated course codes for transfer plan')
    
    args = parser.parse_args()
    
    system = CourseEquivalencySystem()
    
    if not system.setup_environment():
        return
    
    if args.mode == 'sample':
        print("🔄 Generating sample course data...")
        system.generate_sample_data()
        print("✅ Sample data generated! Run with --mode analyze to analyze courses.")
        
    elif args.mode == 'scrape':
        print("🔄 Scraping real course data from universities...")
        system.scrape_real_data()
        print("✅ Real data scraped! Run with --mode analyze to analyze courses.")
        
    elif args.mode == 'analyze':
        print(f"🔄 Analyzing course equivalencies (threshold: {args.threshold})...")
        system.analyze_courses(args.threshold)
        print("✅ Analysis complete! Check course_matches.csv for results.")
        
    elif args.mode == 'transfer':
        if not all([args.source, args.target, args.courses]):
            print("❌ Transfer mode requires --source, --target, and --courses arguments")
            return
        
        print(f"🔄 Generating transfer plan...")
        system.generate_transfer_plan(args.source, args.target, args.courses)
        
    elif args.mode == 'dashboard':
        print("🚀 Launching web dashboard...")
        system.run_dashboard()

if __name__ == "__main__":
    main()
