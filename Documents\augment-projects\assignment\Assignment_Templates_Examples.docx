Wedding Planning Scrum Assignment - Templates & Examples

Template 1: Product Backlog Template

Epic: Venue Selection
User Story ID | As a... | I want... | So that... | Priority | Story Points | Acceptance Criteria
WP-001 | Couple | To research venues within 30 miles | We can find options that fit our guest count | High | 5 | List of 15+ venues with capacity, pricing, availability
WP-002 | Bride | To visit top 5 venues in person | I can visualize our ceremony there | High | 8 | Completed venue visit checklist for each location
WP-003 | Groom | To negotiate contract terms | We get the best value for our budget | Medium | 3 | Signed contract with favorable terms

Epic: Guest Management
User Story ID | As a... | I want... | So that... | Priority | Story Points | Acceptance Criteria
WP-004 | Couple | To create a master guest list | We know exactly who to invite | High | 5 | Categorized list with contact information
WP-005 | Bride | To design save-the-dates | Guests can plan ahead | Medium | 3 | Approved design ready for printing
WP-006 | Family | To track RSVPs | We can finalize headcount | High | 2 | RSVP tracking system with meal preferences

Template 2: Sprint Planning Document

Sprint Number: 1
Sprint Duration: March 1-14, 2024 (2 weeks)
Sprint Goal: Secure venue and begin vendor outreach

Team Capacity
• Product Owner (Bride): 10 hours/week
• Scrum Master (Groom): 8 hours/week
• Team Member 1 (Maid of Honor): 5 hours/week
• Team Member 2 (Best Man): 3 hours/week

Sprint Backlog
Task | Assigned To | Estimated Hours | Status
Research 10 venues | Bride | 6 | Not Started
Create venue comparison spreadsheet | Groom | 2 | Not Started
Schedule venue visits | Maid of Honor | 3 | Not Started
Contact photographers for quotes | Best Man | 4 | Not Started

Definition of Done
☐ All venue research documented
☐ Minimum 3 venues visited
☐ Venue decision made and communicated to stakeholders
☐ Contract negotiations initiated

Template 3: Daily Scrum Log

Day 1 - March 1, 2024
Participants: Bride, Groom, Maid of Honor, Best Man

Bride (Product Owner):
• Yesterday: Set up project workspace, defined initial vision
• Today: Begin venue research, create evaluation criteria
• Blockers: Need to confirm guest count with parents

Groom (Scrum Master):
• Yesterday: Scheduled team kickoff meeting
• Today: Create venue comparison template, research pricing
• Blockers: None

Maid of Honor:
• Yesterday: Reviewed assignment requirements
• Today: Start calling venues for availability
• Blockers: Need venue list from bride first

Best Man:
• Yesterday: Nothing (sprint just started)
• Today: Research photographer portfolios online
• Blockers: None

Template 4: User Story Examples with Acceptance Criteria

User Story: Catering Selection
As a couple planning our wedding
I want to taste-test menu options from 3 different caterers
So that we can select food that our guests will enjoy and fits our budget

Acceptance Criteria:
☐ Research and contact minimum 5 catering companies
☐ Schedule tasting appointments with top 3 choices
☐ Complete tasting evaluation form for each option
☐ Compare pricing for our expected guest count
☐ Make final catering decision within 1 week of last tasting
☐ Negotiate contract terms including cancellation policy

Story Points: 8
Priority: High
Dependencies: Guest count finalization, venue selection (kitchen facilities)

User Story: Photography Package
As a bride and groom
I want to review photographer portfolios and meet potential photographers
So that we can capture our special day with someone whose style matches our vision

Acceptance Criteria:
☐ Review online portfolios of 10+ photographers
☐ Narrow down to 3 finalists based on style and budget
☐ Schedule in-person or video meetings with finalists
☐ Check references from recent clients
☐ Compare package options and pricing
☐ Book photographer with signed contract

Story Points: 5
Priority: High
Dependencies: Budget approval, venue confirmation for timeline planning

Template 5: Sprint Retrospective Format

Sprint 1 Retrospective - March 14, 2024

What Went Well? 🟢
• Team communication was excellent throughout the sprint
• Venue research was more thorough than expected
• Stakeholder feedback helped refine our criteria
• Daily check-ins kept everyone aligned

What Could Be Improved? 🟡
• Initial story point estimates were too optimistic
• Need better tools for tracking vendor communications
• Should involve parents earlier in decision-making process
• Time management during venue visits could be better

What Didn't Work? 🔴
• Photographer research took longer than expected
• Venue availability was more limited than anticipated
• Budget discussions created some tension
• Weekend work disrupted personal time

Action Items for Next Sprint
1. Adjust estimation process: Use planning poker for more accurate story points
2. Implement vendor tracking: Create shared spreadsheet for all vendor communications
3. Schedule stakeholder meeting: Weekly check-ins with parents/key family members
4. Time-box activities: Set maximum time limits for research tasks

Team Mood Check
Rate your experience this sprint (1-5 scale):
• Productivity: 4/5
• Collaboration: 5/5
• Stress Level: 3/5
• Satisfaction: 4/5

Template 6: Risk Register

Risk ID | Risk Description | Probability | Impact | Risk Score | Mitigation Strategy | Owner
R-001 | Venue cancellation due to COVID | Medium | High | 15 | Backup venue identified, flexible contract terms | Bride
R-002 | Key vendor unavailable on wedding date | Low | High | 10 | Book vendors 12+ months in advance | Groom
R-003 | Budget overrun by 20%+ | High | Medium | 12 | Weekly budget reviews, contingency fund | Both
R-004 | Weather issues for outdoor ceremony | Medium | Medium | 9 | Indoor backup plan, tent rental option | Bride
R-005 | Key family member unable to attend | Low | Medium | 6 | Flexible ceremony roles, live streaming option | Both

Risk Score = Probability (1-5) × Impact (1-5)

Sample Velocity Tracking Chart

Sprint Velocity Data
Sprint | Planned Story Points | Completed Story Points | Team Capacity (hours) | Velocity
1 | 25 | 20 | 52 | 20
2 | 22 | 24 | 48 | 24
3 | 26 | 23 | 50 | 23
4 | 24 | 26 | 54 | 26

Average Velocity: 23.25 story points per sprint
Trend: Increasing velocity as team learns to work together
Capacity Utilization: 85-95% (healthy range)

Reflection Questions for Final Essay

Process Effectiveness
1. How did breaking down wedding planning into sprints change your approach to the project?
2. Which Scrum ceremonies provided the most value? Why?
3. What surprised you most about applying agile principles to personal projects?

Stakeholder Management
4. How did regular stakeholder feedback improve your planning process?
5. What challenges arose when family members had conflicting priorities?
6. How would you handle scope creep in future personal projects?

Professional Applications
7. Which skills from this exercise would transfer directly to workplace projects?
8. How might you adapt these techniques for different types of projects?
9. What aspects of Scrum felt most/least natural in this context?

Personal Growth
10. How did this structured approach affect your stress levels during planning?
11. What would you do differently if starting this project again?
12. How has this experience changed your perspective on project management?

Bonus: Creative Presentation Ideas

Option 1: Wedding Planning Board Demo
Create a live demonstration of your Scrum board (Trello, Jira, etc.) showing:
• How tasks moved through workflow states
• Sprint burndown charts
• Stakeholder feedback integration

Option 2: Before/After Comparison
Present a side-by-side comparison of:
• Traditional wedding planning approach vs. Scrum methodology
• Stress levels, budget adherence, timeline management
• Stakeholder satisfaction and involvement

Option 3: Lessons Learned Storytelling
Structure your presentation as a narrative journey:
• The challenge of wedding planning
• Discovering Scrum as a solution
• Key turning points and breakthroughs
• Final outcomes and future applications

Remember: The most engaging presentations include specific examples, real challenges you faced, and honest reflections on what worked and what didn't.
