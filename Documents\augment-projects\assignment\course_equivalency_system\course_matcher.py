"""
Course Equivalency Matching System
Uses NLP and ML techniques to match similar courses across universities
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
import re
from fuzzywuzzy import fuzz
import json
import logging

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
except:
    pass

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CourseEquivalencyMatcher:
    def __init__(self):
        self.lemmatizer = WordNetLemmatizer()
        self.stop_words = set(stopwords.words('english'))
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        
        # Load pre-trained sentence transformer for semantic similarity
        try:
            self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
        except:
            logger.warning("Could not load sentence transformer model")
            self.sentence_model = None
        
        # Course equivalency rules and weights
        self.weights = {
            'course_name_similarity': 0.35,
            'description_similarity': 0.25,
            'code_similarity': 0.15,
            'credit_similarity': 0.15,
            'semantic_similarity': 0.10
        }
        
        # Common course keywords and their variations
        self.course_keywords = {
            'programming': ['programming', 'coding', 'software development', 'algorithms'],
            'mathematics': ['mathematics', 'math', 'calculus', 'algebra', 'statistics'],
            'database': ['database', 'data management', 'sql', 'dbms'],
            'networks': ['network', 'networking', 'internet', 'protocols'],
            'ai': ['artificial intelligence', 'machine learning', 'deep learning', 'neural networks'],
            'graphics': ['computer graphics', 'visualization', 'rendering', 'graphics programming'],
            'systems': ['operating systems', 'system programming', 'computer systems'],
            'security': ['cybersecurity', 'information security', 'cryptography', 'security']
        }
    
    def preprocess_text(self, text):
        """Preprocess text for better matching"""
        if not text or pd.isna(text):
            return ""
        
        # Convert to lowercase
        text = str(text).lower()
        
        # Remove special characters and numbers (except in course codes)
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # Tokenize
        tokens = word_tokenize(text)
        
        # Remove stopwords and lemmatize
        tokens = [self.lemmatizer.lemmatize(token) for token in tokens 
                 if token not in self.stop_words and len(token) > 2]
        
        return ' '.join(tokens)
    
    def extract_course_code_similarity(self, code1, code2):
        """Calculate similarity between course codes"""
        if not code1 or not code2 or pd.isna(code1) or pd.isna(code2):
            return 0.0
        
        code1 = str(code1).upper().strip()
        code2 = str(code2).upper().strip()
        
        # Extract letters and numbers separately
        letters1 = re.findall(r'[A-Z]+', code1)
        numbers1 = re.findall(r'\d+', code1)
        letters2 = re.findall(r'[A-Z]+', code2)
        numbers2 = re.findall(r'\d+', code2)
        
        letter_similarity = 0.0
        number_similarity = 0.0
        
        # Compare letters (department codes)
        if letters1 and letters2:
            letter_similarity = fuzz.ratio(letters1[0], letters2[0]) / 100.0
        
        # Compare numbers (course levels)
        if numbers1 and numbers2:
            num1, num2 = int(numbers1[0]), int(numbers2[0])
            # Same level courses (100s, 200s, etc.)
            if num1 // 100 == num2 // 100:
                number_similarity = 1.0 - abs(num1 - num2) / 100.0
            else:
                number_similarity = max(0, 1.0 - abs(num1 - num2) / 500.0)
        
        return (letter_similarity + number_similarity) / 2.0
    
    def calculate_credit_similarity(self, credits1, credits2):
        """Calculate similarity based on credit hours"""
        if pd.isna(credits1) or pd.isna(credits2):
            return 0.5  # Neutral score if credits unknown
        
        credits1, credits2 = int(credits1), int(credits2)
        
        if credits1 == credits2:
            return 1.0
        elif abs(credits1 - credits2) == 1:
            return 0.8
        elif abs(credits1 - credits2) == 2:
            return 0.6
        else:
            return max(0, 1.0 - abs(credits1 - credits2) / 6.0)
    
    def calculate_text_similarity(self, text1, text2):
        """Calculate text similarity using multiple methods"""
        if not text1 or not text2 or pd.isna(text1) or pd.isna(text2):
            return 0.0
        
        # Preprocess texts
        processed_text1 = self.preprocess_text(text1)
        processed_text2 = self.preprocess_text(text2)
        
        if not processed_text1 or not processed_text2:
            return 0.0
        
        # Fuzzy string matching
        fuzzy_score = fuzz.token_sort_ratio(processed_text1, processed_text2) / 100.0
        
        # TF-IDF similarity
        try:
            tfidf_matrix = self.tfidf_vectorizer.fit_transform([processed_text1, processed_text2])
            tfidf_similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        except:
            tfidf_similarity = 0.0
        
        # Combine scores
        return (fuzzy_score + tfidf_similarity) / 2.0
    
    def calculate_semantic_similarity(self, text1, text2):
        """Calculate semantic similarity using sentence transformers"""
        if not self.sentence_model or not text1 or not text2:
            return 0.0
        
        try:
            embeddings = self.sentence_model.encode([str(text1), str(text2)])
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            return max(0.0, similarity)
        except:
            return 0.0
    
    def calculate_course_similarity(self, course1, course2):
        """Calculate overall similarity between two courses"""
        similarities = {}
        
        # Course name similarity
        similarities['course_name_similarity'] = self.calculate_text_similarity(
            course1.get('course_name', ''), course2.get('course_name', '')
        )
        
        # Description similarity
        similarities['description_similarity'] = self.calculate_text_similarity(
            course1.get('description', ''), course2.get('description', '')
        )
        
        # Course code similarity
        similarities['code_similarity'] = self.extract_course_code_similarity(
            course1.get('course_code', ''), course2.get('course_code', '')
        )
        
        # Credit similarity
        similarities['credit_similarity'] = self.calculate_credit_similarity(
            course1.get('credits', 0), course2.get('credits', 0)
        )
        
        # Semantic similarity
        combined_text1 = f"{course1.get('course_name', '')} {course1.get('description', '')}"
        combined_text2 = f"{course2.get('course_name', '')} {course2.get('description', '')}"
        similarities['semantic_similarity'] = self.calculate_semantic_similarity(
            combined_text1, combined_text2
        )
        
        # Calculate weighted overall similarity
        overall_similarity = sum(
            similarities[key] * self.weights[key] 
            for key in similarities.keys()
        )
        
        return overall_similarity, similarities
    
    def find_course_matches(self, courses_df, min_similarity=0.6):
        """Find potential course matches across universities"""
        matches = []
        
        # Group courses by university
        universities = courses_df['university'].unique()
        
        for i, uni1 in enumerate(universities):
            for uni2 in universities[i+1:]:
                uni1_courses = courses_df[courses_df['university'] == uni1]
                uni2_courses = courses_df[courses_df['university'] == uni2]
                
                logger.info(f"Comparing {uni1} ({len(uni1_courses)} courses) with {uni2} ({len(uni2_courses)} courses)")
                
                for _, course1 in uni1_courses.iterrows():
                    best_match = None
                    best_similarity = 0.0
                    best_details = {}
                    
                    for _, course2 in uni2_courses.iterrows():
                        # Skip if different programs
                        if course1['program'] != course2['program']:
                            continue
                        
                        similarity, details = self.calculate_course_similarity(
                            course1.to_dict(), course2.to_dict()
                        )
                        
                        if similarity > best_similarity and similarity >= min_similarity:
                            best_similarity = similarity
                            best_match = course2
                            best_details = details
                    
                    if best_match is not None:
                        match = {
                            'university1': uni1,
                            'course1_code': course1['course_code'],
                            'course1_name': course1['course_name'],
                            'course1_credits': course1.get('credits', 0),
                            'university2': uni2,
                            'course2_code': best_match['course_code'],
                            'course2_name': best_match['course_name'],
                            'course2_credits': best_match.get('credits', 0),
                            'program': course1['program'],
                            'overall_similarity': best_similarity,
                            'similarity_details': best_details,
                            'confidence': self.get_confidence_level(best_similarity),
                            'recommendation': self.get_recommendation(best_similarity, best_details)
                        }
                        matches.append(match)
        
        return pd.DataFrame(matches)
    
    def get_confidence_level(self, similarity):
        """Get confidence level based on similarity score"""
        if similarity >= 0.9:
            return "Very High"
        elif similarity >= 0.8:
            return "High"
        elif similarity >= 0.7:
            return "Medium"
        elif similarity >= 0.6:
            return "Low"
        else:
            return "Very Low"
    
    def get_recommendation(self, similarity, details):
        """Get transfer recommendation based on similarity analysis"""
        if similarity >= 0.9:
            return "Direct Transfer - Highly Recommended"
        elif similarity >= 0.8:
            return "Transfer with Minor Review"
        elif similarity >= 0.7:
            return "Transfer with Academic Review Required"
        elif similarity >= 0.6:
            return "Conditional Transfer - Detailed Review Needed"
        else:
            return "Not Recommended for Transfer"
    
    def generate_transfer_plan(self, matches_df, source_uni, target_uni, student_courses):
        """Generate a transfer plan for a student"""
        relevant_matches = matches_df[
            ((matches_df['university1'] == source_uni) & (matches_df['university2'] == target_uni)) |
            ((matches_df['university1'] == target_uni) & (matches_df['university2'] == source_uni))
        ]
        
        transfer_plan = []
        total_credits_original = 0
        total_credits_transferred = 0
        
        for course_code in student_courses:
            match = relevant_matches[
                (relevant_matches['course1_code'] == course_code) |
                (relevant_matches['course2_code'] == course_code)
            ]
            
            if not match.empty:
                match_row = match.iloc[0]
                if match_row['course1_code'] == course_code:
                    original_credits = match_row['course1_credits']
                    transfer_credits = match_row['course2_credits']
                    transfer_to = match_row['course2_code']
                else:
                    original_credits = match_row['course2_credits']
                    transfer_credits = match_row['course1_credits']
                    transfer_to = match_row['course1_code']
                
                total_credits_original += original_credits
                total_credits_transferred += transfer_credits
                
                transfer_plan.append({
                    'original_course': course_code,
                    'transfer_to': transfer_to,
                    'original_credits': original_credits,
                    'transfer_credits': transfer_credits,
                    'similarity': match_row['overall_similarity'],
                    'confidence': match_row['confidence'],
                    'recommendation': match_row['recommendation']
                })
        
        return {
            'transfer_plan': transfer_plan,
            'summary': {
                'total_courses': len(student_courses),
                'transferable_courses': len(transfer_plan),
                'original_credits': total_credits_original,
                'transferred_credits': total_credits_transferred,
                'credit_loss': total_credits_original - total_credits_transferred
            }
        }
    
    def save_matches(self, matches_df, filename='course_matches.csv'):
        """Save matches to CSV file"""
        matches_df.to_csv(filename, index=False)
        logger.info(f"Saved {len(matches_df)} matches to {filename}")
        
    def save_matches_json(self, matches_df, filename='course_matches.json'):
        """Save matches to JSON file"""
        matches_dict = matches_df.to_dict('records')
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(matches_dict, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved {len(matches_df)} matches to {filename}")

if __name__ == "__main__":
    # Example usage
    matcher = CourseEquivalencyMatcher()
    
    # Load course data
    try:
        courses_df = pd.read_csv('university_courses.csv')
        logger.info(f"Loaded {len(courses_df)} courses")
        
        # Find matches
        matches_df = matcher.find_course_matches(courses_df)
        logger.info(f"Found {len(matches_df)} potential matches")
        
        # Save results
        matcher.save_matches(matches_df)
        matcher.save_matches_json(matches_df)
        
        # Display summary
        print(f"\nCourse Matching Summary:")
        print(f"Total matches found: {len(matches_df)}")
        print(f"Confidence distribution:")
        print(matches_df['confidence'].value_counts())
        
    except FileNotFoundError:
        logger.error("Course data file not found. Please run data_scraper.py first.")
