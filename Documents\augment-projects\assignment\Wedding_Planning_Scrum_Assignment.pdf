Wedding Planning Using Scrum Methodology
MVP Project Implementation

Student: <PERSON>
Course: Project Management 301
Date: March 15, 2024

PROJECT OVERVIEW
• Project Goal: Plan dream wedding using Scrum framework
• Duration: 8 months (engagement to wedding)
• Budget: $25,000 maximum
• Guest Count: 75-85 people
• Location: Within 50 miles of hometown
• Season: Late September (fall colors)
• Approach: Agile/Scrum methodology with 2-week sprints

WHY SCRUM FOR WEDDING PLANNING?

Traditional Wedding Planning Challenges:
• Overwhelming number of tasks and decisions
• Multiple stakeholders with different priorities
• Budget constraints and timeline pressure
• Vendor coordination complexity
• Scope creep and changing requirements

Scrum Solutions:
• Break down into manageable sprints
• Regular stakeholder feedback loops
• Iterative planning and adaptation
• Clear roles and responsibilities
• Continuous improvement through retrospectives

RELEVANT PERSONNEL

Product Owner (wedding planner)

<PERSON><PERSON> Master (wedding coordinator)

Development Team (wedding team)

PRODUCT VISION & SUCCESS CRITERIA

Wedding Vision:
Intimate outdoor celebration reflecting our love for nature, bringing together closest family and friends in a relaxed, joyful atmosphere where guests feel comfortable and can truly celebrate with us.

Success Criteria:
• Stay within budget with 5% buffer
• All guests feel welcomed and enjoy celebration
• Ceremony and reception flow smoothly
• <PERSON><PERSON><PERSON> feels relaxed and present during event
• Professional photos capture key moments beautifully

Key Constraints:
• Budget: $25,000 maximum
• Timeline: 8 months planning period
• Guest count: 75-85 people maximum
• Location: Within 50 miles of hometown
• Weather dependency for outdoor ceremony

EPIC BREAKDOWN

Epic 1: Venue Selection and Setup (50 story points)
• Research potential venues within budget
• Schedule and conduct site visits
• Negotiate contracts and secure booking
• Plan ceremony and reception layouts

Epic 2: Vendor Management (45 story points)
• Book photographer, caterer, florist
• Select music/entertainment options
• Coordinate officiant and ceremony details
• Arrange transportation and logistics

Epic 3: Guest Experience (35 story points)
• Create and manage guest list
• Design and send invitations
• Plan reception activities and flow
• Coordinate accommodations and welcome

Epic 4: Personal Preparation (30 story points)
• Select wedding attire and accessories
• Plan beauty services and preparations
• Write personal vows and practice
• Organize honeymoon and post-wedding

SPRINT PLANNING OVERVIEW

Sprint 1: Venue Selection Focus (March 1-14)
Goal: Secure perfect venue aligning with vision and budget
Stories: Research venues, conduct visits, negotiate contract
Team Capacity: 34 hours
Key Deliverables: Signed venue contract with favorable terms

Sprint 2: Key Vendor Selection (March 15-28)
Goal: Book photographer, caterer, and florist
Stories: Vendor research, meetings, tastings, contracts
Team Capacity: 32 hours
Key Deliverables: Core vendors booked and confirmed

Sprint 3: Guest Management (March 29 - April 11)
Goal: Finalize guest list and send invitations
Stories: Guest list creation, invitation design, mailing
Team Capacity: 28 hours
Key Deliverables: Invitations sent, RSVP system active

Sprint 4: Personal Preparation (April 12-25)
Goal: Complete couple's personal wedding preparations
Stories: Attire selection, beauty planning, vow writing
Team Capacity: 30 hours
Key Deliverables: Personal elements finalized

SAMPLE USER STORIES

Story 1: Venue Research
As a couple, I want to research venues within 30 miles so that we can find options that fit our guest count and budget.

Acceptance Criteria:
• List of 15+ venues with capacity, pricing, availability
• Venues categorized by style (outdoor, indoor, hybrid)
• Contact information and booking requirements documented
Story Points: 8 | Priority: High | Sprint: 1

Story 2: Photography Selection
As a couple, I want to review photographer portfolios and book our photographer so that we capture our special day beautifully.

Acceptance Criteria:
• Review 15+ photographer portfolios
• Meet with top 3 finalists
• Check references from recent clients
• Sign contract with chosen photographer
Story Points: 8 | Priority: High | Sprint: 2

Story 3: Catering Selection
As a couple, I want to taste-test menu options from different caterers so that we provide delicious food for our guests.

Acceptance Criteria:
• Research 8+ catering companies
• Schedule tastings with top 4 caterers
• Accommodate dietary restrictions
• Finalize menu and service style
Story Points: 10 | Priority: High | Sprint: 2

DAILY SCRUM EXAMPLE

March 5, 2024 Daily Scrum:

Sarah (Product Owner):
Yesterday: Completed research on 10 venues, narrowed to 8 viable options
Today: Review Jessica's availability feedback and finalize visit list
Blockers: None

Michael (Scrum Master):
Yesterday: Finished comparison spreadsheet and researched pricing
Today: Help Sarah evaluate venues and prepare visit questions
Blockers: None

Jessica (Maid of Honor):
Yesterday: Contacted 8 venues, got availability for 6
Today: Schedule visits for the 5 best options
Blockers: None

David (Best Man):
Yesterday: Mapped transportation routes to venues
Today: Coordinate schedules for venue visits
Blockers: None

SPRINT 1 RESULTS

Sprint Goal: Secure perfect venue aligning with vision and budget
Status: COMPLETED SUCCESSFULLY

Achievements:
• Researched 15 potential venues
• Visited 5 top venues with detailed evaluations
• Gathered comprehensive stakeholder feedback
• Identified Riverside Gardens as top choice
• Negotiated 10% discount on venue package
• Signed contract with favorable terms and weather contingency

Metrics:
• Planned Story Points: 26
• Completed Story Points: 26
• Team Velocity: 26 points
• Budget Impact: $8,500 (34% of total budget)
• Timeline: Completed on schedule

SPRINT RETROSPECTIVE INSIGHTS

What Went Well:
• Team communication was excellent throughout sprint
• Venue visits were well-organized and productive
• Family stakeholders provided valuable input
• Stayed within planned timeline and budget
• Negotiated better terms than expected

What Could Be Improved:
• Initial venue research took longer than estimated
• Some venues had limited availability for preferred date
• Need better system for tracking vendor communications
• Should involve parents earlier in evaluation process

Action Items for Next Sprint:
• Implement shared calendar for all team activities
• Create vendor contact tracking spreadsheet
• Schedule weekly stakeholder check-ins with parents
• Adjust story point estimates based on actual time spent

VELOCITY TRACKING

Sprint Performance:
Sprint 1: Planned 26 pts | Completed 26 pts | Velocity: 26
Sprint 2: Planned 24 pts | Completed 28 pts | Velocity: 28
Sprint 3: Planned 22 pts | Completed 20 pts | Velocity: 20
Sprint 4: Planned 26 pts | Completed 30 pts | Velocity: 30

Average Team Velocity: 26 story points per sprint
Trend: Improving efficiency and estimation accuracy
Capacity Utilization: 85-95% (healthy range)

RISK MANAGEMENT

High Priority Risks:
• Weather issues for outdoor ceremony
  Mitigation: Indoor backup space secured, tent rental on standby
• Key vendor cancellation
  Mitigation: Backup vendor list maintained, replacement clauses in contracts
• Budget overrun
  Mitigation: Weekly budget reviews, 10% contingency fund allocated

Medium Priority Risks:
• Guest count changes affecting catering
  Mitigation: Final headcount deadline 2 weeks before wedding
• Transportation delays on wedding day
  Mitigation: Buffer time in timeline, backup transportation arranged

PROFESSIONAL APPLICATIONS

Skills Transferred to Workplace:
• Breaking complex projects into manageable iterations
• Facilitating stakeholder communication and feedback loops
• Using retrospectives to continuously improve processes
• Managing scope creep through clear prioritization
• Collaborative planning and regular communication rhythms

Scrum Principles Most Valuable:
• Iterative planning reduces overwhelming complexity
• Regular stakeholder feedback prevents costly mistakes
• Time-boxing forces better decision-making
• Team retrospectives identify process improvements
• Visual project tracking keeps everyone aligned

KEY LEARNINGS & REFLECTION

Methodology Comparison:
Traditional wedding planning would have been overwhelming and stressful. Scrum's iterative approach made the complex project manageable and actually enjoyable. Regular stakeholder feedback prevented major course corrections and ensured everyone felt heard.

Most Surprising Discovery:
The collaborative planning approach brought our families together in unexpected ways. Daily check-ins and sprint reviews created shared ownership of the wedding planning process.

Critical Success Factors:
1. Regular stakeholder engagement prevents major issues
2. Time-boxing activities forces better decision-making
3. Team retrospectives save time through process improvements
4. Visual project tracking maintains team alignment
5. Iterative planning allows adaptation to changing requirements

FINAL RESULTS & CELEBRATION

Wedding Day Success Metrics:
✓ Budget: $24,200 (3.2% under budget)
✓ Timeline: All milestones met on schedule
✓ Guest Satisfaction: 98% positive feedback
✓ Vendor Performance: All vendors exceeded expectations
✓ Couple Experience: Relaxed and present throughout day
✓ Photo Quality: Beautiful memories captured professionally

Scrum Methodology Impact:
• 40% less stress compared to traditional planning
• 100% stakeholder satisfaction with process
• Zero major issues on wedding day
• Stronger team relationships developed
• Valuable skills gained for professional application

CONCLUSION

The Scrum framework transformed our wedding planning from a potentially overwhelming experience into an organized, collaborative journey that brought our families together and resulted in our perfect wedding day.

This MVP approach to wedding planning using Scrum methodology demonstrates how agile principles can be successfully applied beyond software development to personal projects, creating better outcomes with less stress and more stakeholder satisfaction.
