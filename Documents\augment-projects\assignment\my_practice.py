# My Python Practice File
# Complete the exercises below!

print("Welcome to my Python practice!")
print("welcome to my new file for python practice")

# Exercise 1: Create a variable with your favorite color
# Example: favorite_color = "blue"
# Your code here:
favorite_color = "red"
print(favorite_color) 

# Exercise 2: Make a list of your hobbies
# Example: hobbies = ["reading", "gaming", "cooking"]
# Your code here:
hobbies = [ "reading", "gaming", "cooking" , "pling" , "swimmig"]
print (hobbies) 


# Exercise 3: Write a function that multiplies two numbers
# Example:
# def multiply(a, b):
#     return a * b
# Your code here:
def multiply (a,b):
    return a * b
print (multiply(2,5))




# Exercise 4: Use an if statement to check if a number is even or odd
# Hint: Use the % operator. If number % 2 == 0, it's even
# Example:
# number = 7
# if number % 2 == 0:
#     print(number, "is even")
# else:
#     print(number, "is odd")
# Your code here:

number = 7 
if number % 2 == 0:
    print(number, "is even")
else:
    print(number, "is odd")   

# Exercise 5: Create a loop that prints numbers from 1 to 10
# Hint: Use range(1, 11) to get numbers 1 through 10
# Your code here:
for i in range(1,11): 
    print(i)
    

print("Great job practicing Python!")

#50

# ========== NEW PRACTICE EXERCISES ==========
# Exercise 6: Dictionaries (like a phone book)
# Dictionaries store key-value pairs
person = {
    "name": "Sarah",
    "age": 28,
    "city": "New York"
}
print("person info:", person)
print("Name:", person["name"])

numbers = [1, 5, 3, 9, 2]
print("Original numbers:", numbers)
numbers.sort() # Sorts the list in ascending order

print("Sorted numbers:", numbers)
numbers.reverse() # Reverses the order of the list
print("Reversed numbers:", numbers)

squares1 = [x**2 for x in range(1, 6)] # square nubers 1-5
print ("squares:", squares1)

name = "fadhl"
score = 95
message = f"Hello {name}, your score is {score}%"
print(message)

try:
    result = 10 / 0 # This will cause an error
except ZeroDivisionError:
    print("cannot divide by zero!")


/#50



