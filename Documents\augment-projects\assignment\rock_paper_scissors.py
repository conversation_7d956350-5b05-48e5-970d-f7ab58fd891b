# Rock Paper Scissors Game
# Play against the computer!

import random

def rock_paper_scissors():
    print("🪨📄✂️ Rock Paper Scissors Game! 🪨📄✂️")
    print("Rules: Rock beats Scissors, Scissors beats Paper, Paper beats Rock")
    
    # Game choices
    choices = ["rock", "paper", "scissors"]
    
    # Score tracking
    player_score = 0
    computer_score = 0
    rounds = 0
    
    while True:
        print(f"\n--- Round {rounds + 1} ---")
        print(f"Score: You {player_score} - {computer_score} Computer")
        
        # Get player choice
        print("\nChoose your weapon:")
        print("1. Rock 🪨")
        print("2. Paper 📄") 
        print("3. Scissors ✂️")
        print("4. Quit game")
        
        try:
            choice = input("Enter your choice (1-4): ")
            
            if choice == "4":
                break
            elif choice in ["1", "2", "3"]:
                player_choice = choices[int(choice) - 1]
            else:
                print("❌ Invalid choice! Please enter 1, 2, 3, or 4")
                continue
                
        except (ValueError, IndexError):
            print("❌ Invalid input! Please enter a number 1-4")
            continue
        
        # Computer makes random choice
        computer_choice = random.choice(choices)
        
        # Show choices
        print(f"\nYou chose: {player_choice.upper()} ")
        print(f"Computer chose: {computer_choice.upper()}")
        
        # Determine winner
        if player_choice == computer_choice:
            print("🤝 It's a tie!")
        elif (player_choice == "rock" and computer_choice == "scissors") or \
             (player_choice == "paper" and computer_choice == "rock") or \
             (player_choice == "scissors" and computer_choice == "paper"):
            print("🎉 You win this round!")
            player_score += 1
        else:
            print("💻 Computer wins this round!")
            computer_score += 1
        
        rounds += 1
        
        # Check for game winner (first to 3 wins)
        if player_score == 3:
            print(f"\n🏆 GAME OVER! You won {player_score}-{computer_score}!")
            break
        elif computer_score == 3:
            print(f"\n💻 GAME OVER! Computer won {computer_score}-{player_score}!")
            break
    
    print(f"\nFinal Score: You {player_score} - {computer_score} Computer")
    print("Thanks for playing! 👋")

# Start the game
if __name__ == "__main__":
    rock_paper_scissors()
    
    # Ask if they want to play again
    while True:
        play_again = input("\nWant to play again? (yes/no): ").lower()
        if play_again in ['yes', 'y']:
            rock_paper_scissors()
        elif play_again in ['no', 'n']:
            print("See you next time! 🎮")
            break
        else:
            print("Please enter 'yes' or 'no'")
