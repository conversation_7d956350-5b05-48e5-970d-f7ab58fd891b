Instructor Guide: Wedding Planning Scrum Assignment

Assignment Rationale

This assignment bridges theoretical Scrum knowledge with practical application through a relatable, complex project scenario. Wedding planning provides an ideal context because it involves:

• Multiple stakeholders with varying priorities
• Budget constraints and timeline pressures
• Iterative decision-making processes
• Risk management and contingency planning
• Scope creep and changing requirements

Students engage more deeply when learning through personally meaningful contexts, leading to better retention of Scrum principles and practices.

Learning Outcomes Assessment

Primary Learning Objectives
By completing this assignment, students will demonstrate ability to:

1. Apply Scrum Framework: Correctly implement roles, artifacts, and ceremonies
2. Manage Complex Projects: Break down overwhelming tasks into manageable sprints
3. Facilitate Stakeholder Engagement: Navigate competing priorities and feedback
4. Adapt Agile Principles: Transfer methodology beyond software development contexts
5. Reflect on Process Improvement: Analyze effectiveness and identify optimization opportunities

Assessment Alignment
Each deliverable maps to specific learning outcomes:
• Vision document → Strategic thinking and stakeholder analysis
• Product backlog → Requirements gathering and prioritization
• Sprint artifacts → Iterative planning and execution
• Retrospectives → Continuous improvement mindset
• Reflection essay → Critical analysis and professional application

Detailed Grading Rubric

Vision & Planning (25 points)

Excellent (23-25 points):
• Vision statement is clear, compelling, and addresses all stakeholder needs
• Comprehensive stakeholder analysis with detailed role definitions
• Realistic constraints and success criteria identified
• Professional presentation with supporting documentation

Good (20-22 points):
• Vision statement covers most key elements with minor gaps
• Good stakeholder identification with basic role clarity
• Most constraints identified with reasonable success metrics
• Well-organized presentation with adequate detail

Satisfactory (18-19 points):
• Basic vision statement with limited stakeholder consideration
• Minimal stakeholder analysis or unclear role definitions
• Some constraints identified but success criteria vague
• Acceptable organization but lacks depth

Needs Improvement (0-17 points):
• Unclear or incomplete vision statement
• Poor stakeholder identification or missing role definitions
• Unrealistic or missing constraints and success criteria
• Disorganized presentation with significant gaps

Scrum Artifacts (30 points)

Excellent (27-30 points):
• Product backlog contains 50+ well-written user stories with clear acceptance criteria
• Stories properly prioritized using consistent methodology
• Sprint planning documents show realistic capacity and task breakdown
• All artifacts follow Scrum best practices and professional standards

Good (24-26 points):
• Product backlog contains 40+ user stories with mostly clear acceptance criteria
• Good prioritization with minor inconsistencies
• Sprint planning shows reasonable capacity estimation
• Most artifacts follow Scrum practices with minor deviations

Satisfactory (21-23 points):
• Product backlog contains 30+ user stories with basic acceptance criteria
• Basic prioritization with some unclear rationale
• Sprint planning shows limited capacity consideration
• Artifacts demonstrate basic understanding but lack polish

Needs Improvement (0-20 points):
• Insufficient user stories or poorly written acceptance criteria
• No clear prioritization methodology
• Sprint planning lacks realistic capacity estimation
• Artifacts show limited understanding of Scrum practices

Process Execution (25 points)

Excellent (23-25 points):
• Daily scrum logs demonstrate consistent team communication
• Sprint reviews show meaningful stakeholder engagement
• Retrospectives identify specific improvements with actionable plans
• Clear evidence of iterative improvement throughout project

Good (20-22 points):
• Daily scrums show good team communication with minor gaps
• Sprint reviews demonstrate stakeholder involvement
• Retrospectives identify improvements with general action plans
• Some evidence of process refinement

Satisfactory (18-19 points):
• Daily scrums show basic team communication
• Sprint reviews demonstrate limited stakeholder engagement
• Retrospectives identify few improvements with vague action plans
• Minimal evidence of process adaptation

Needs Improvement (0-17 points):
• Inconsistent or missing daily scrum documentation
• Poor stakeholder engagement in sprint reviews
• Retrospectives lack meaningful insights or action plans
• No evidence of process improvement

Reflection Quality (20 points)

Excellent (18-20 points):
• Insightful analysis connecting personal experience to professional applications
• Specific examples demonstrating deep understanding of Scrum principles
• Critical evaluation of methodology strengths and limitations
• Clear articulation of lessons learned and future applications

Good (16-17 points):
• Good reflection with meaningful connections to professional context
• Some specific examples showing understanding of Scrum concepts
• Basic evaluation of methodology effectiveness
• General lessons learned with limited future application discussion

Satisfactory (14-15 points):
• Basic reflection with limited professional connections
• Few specific examples or shallow understanding demonstrated
• Minimal evaluation of methodology
• Vague lessons learned with no future application

Needs Improvement (0-13 points):
• Superficial reflection with no professional connections
• No specific examples or evidence of understanding
• No meaningful evaluation of methodology
• Missing or inadequate lessons learned

Common Student Challenges & Solutions

Challenge 1: Overwhelming Scope
Symptom: Students create massive backlogs with unrealistic sprint commitments
Solution: Emphasize MVP thinking and provide examples of appropriate story sizing

Challenge 2: Artificial Stakeholder Feedback
Symptom: Students fabricate stakeholder interactions rather than engaging real people
Solution: Encourage involvement of friends/family or provide structured stakeholder personas

Challenge 3: Superficial Retrospectives
Symptom: Generic "what went well/what didn't" without actionable insights
Solution: Provide specific retrospective techniques and require concrete action items

Challenge 4: Limited Professional Connection
Symptom: Reflection essays focus only on wedding planning without workplace application
Solution: Provide guiding questions that explicitly bridge personal and professional contexts

Extension Activities

For Advanced Students
1. Scaling Exercise: Plan multiple related events (engagement party, bachelor/bachelorette, wedding, reception) using scaled Scrum techniques
2. Metrics Deep Dive: Implement advanced tracking (burndown charts, cumulative flow diagrams, cycle time analysis)
3. Stakeholder Workshop: Facilitate actual stakeholder sessions with family members using Scrum techniques

For Struggling Students
1. Simplified Scope: Focus on single epic (venue selection) with detailed sprint execution
2. Template Support: Provide additional structured templates for all deliverables
3. Peer Collaboration: Allow pair work for artifact creation with individual reflection essays

Technology Recommendations

Free Tools
• Trello: User-friendly Kanban boards with Scrum power-ups
• GitHub Projects: Integrated with development workflows
• Miro/Mural: Collaborative whiteboarding for retrospectives

Paid Tools (if budget allows)
• Jira: Industry-standard with comprehensive Scrum features
• Azure DevOps: Microsoft ecosystem integration
• Monday.com: Intuitive interface with strong visualization

Assessment Considerations
• Don't penalize tool choice - focus on proper Scrum implementation
• Provide tutorials for recommended tools
• Allow alternative documentation methods for students with tool access issues

Timing & Deadlines

Recommended Schedule
• Week 1: Vision creation and team formation
• Week 2: Product backlog development and first sprint planning
• Week 3: Sprint execution with daily scrums and stakeholder feedback
• Week 4: Sprint review, retrospective, and reflection essay completion

Checkpoint Recommendations
• Week 1 Check-in: Review vision statements and provide early feedback
• Week 2 Check-in: Validate product backlog quality and sprint planning
• Week 3 Check-in: Monitor daily scrum execution and address issues
• Final Presentations: Schedule 10-minute presentations for peer learning

Academic Integrity Considerations

Preventing Plagiarism
• Require personal wedding scenarios (real or hypothetical)
• Emphasize unique stakeholder contexts and constraints
• Request process documentation (screenshots, meeting notes) as evidence

Collaboration Guidelines
• Allow discussion of Scrum concepts and techniques
• Require individual artifact creation and reflection
• Clearly define boundaries between collaboration and copying

AI Detection Strategies
• Focus on personal experience and specific examples
• Require process documentation and iterative artifacts
• Emphasize reflection on personal learning journey
• Include presentation component for authentic assessment

Sample Feedback Comments

Positive Reinforcement
• "Your user stories demonstrate excellent understanding of stakeholder perspectives"
• "The iterative refinement shown in your retrospectives reflects true agile thinking"
• "Your professional connections in the reflection essay show deep learning transfer"

Constructive Guidance
• "Consider breaking this epic into smaller, more manageable user stories"
• "Your retrospective would benefit from more specific action items for improvement"
• "Try to include more concrete examples from your sprint execution in your reflection"

Areas for Improvement
• "Your product backlog needs clearer acceptance criteria for each user story"
• "The daily scrum logs should focus more on impediments and collaboration"
• "Your reflection essay would be stronger with more explicit connections to workplace applications"

This assignment successfully combines engaging content with rigorous Scrum methodology assessment, providing students with memorable learning experiences that transfer effectively to professional contexts.
