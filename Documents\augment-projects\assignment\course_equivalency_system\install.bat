@echo off
echo Installing Course Equivalency System Dependencies...
echo.

echo Installing Python packages...
pip install pandas numpy scikit-learn nltk requests beautifulsoup4 streamlit plotly fuzzywuzzy python-Levenshtein sentence-transformers

echo.
echo Downloading NLTK data...
python -c "import nltk; nltk.download('punkt', quiet=True); nltk.download('stopwords', quiet=True); nltk.download('wordnet', quiet=True)"

echo.
echo Installation complete!
echo.
echo To run the demo:
echo python demo.py
echo.
echo To launch the web dashboard:
echo streamlit run dashboard.py
echo.
pause
