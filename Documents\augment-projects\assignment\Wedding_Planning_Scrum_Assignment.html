<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plan Your Wedding with <PERSON>rum - MVP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f9f9f9;
        }
        .header {
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .section {
            background-color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #2c3e50;
        }
        .role-box {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #3498db;
        }
        .backlog-item {
            background-color: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-left: 3px solid #28a745;
            border-radius: 3px;
        }
        .sprint-box {
            background-color: #e8f5e8;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .print-btn {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .print-btn:hover {
            background-color: #2980b9;
        }
        @media print {
            body { background-color: white; }
            .section { box-shadow: none; }
            .print-btn { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Plan Your Wedding with Scrum</h1>
        <h2>MVP Implementation</h2>
        <p><strong>Student:</strong> Fadhl Alsharif | <strong>Student ID:</strong> 22115538</p>
        <button class="print-btn" onclick="window.print()">Print to PDF</button>
        <button class="print-btn" onclick="window.location.reload()">Refresh</button>
    </div>

    <div class="section">
        <h2>Why I Decided to Use Scrum for My Wedding</h2>
        <p>Honestly, when I first got engaged, I was terrified about planning a wedding. My sister's wedding two years ago was a complete disaster - she was stressed out for months, went way over budget, and by the wedding day she was so exhausted she could barely enjoy it. I knew I didn't want that experience.</p>

        <p>After learning about Scrum in one of my classes, I started thinking... why couldn't this work for wedding planning? I mean, it's basically project management, right? So I decided to give it a shot, and honestly, it was one of the best decisions I made.</p>

        <p>Here's what I was hoping Scrum would help me with:</p>
        <ul>
            <li>Not getting overwhelmed by trying to do everything at once</li>
            <li>Actually staying organized (I'm usually pretty scattered)</li>
            <li>Making sure we tackled the important stuff first</li>
            <li>Being able to change our minds without starting over completely</li>
            <li>Keeping my stress levels manageable throughout the process</li>
        </ul>

        <h3>What We Were Working With:</h3>
        <p>Before we started, my partner and I sat down and figured out our constraints:</p>
        <ul>
            <li><strong>Budget:</strong> $25,000 max (our parents were helping, but that was the hard limit)</li>
            <li><strong>Guest Count:</strong> Around 75-85 people - just close family and friends</li>
            <li><strong>Style:</strong> We both love being outdoors, so definitely an outdoor celebration</li>
            <li><strong>Timeline:</strong> We had 8 months from engagement to wedding day</li>
            <li><strong>My Plan:</strong> Try using Scrum with 1-week sprints to stay on track</li>
        </ul>
    </div>

    <div class="section">
        <h2>How We Divided Up the Roles</h2>
        <p>One thing I learned in class is that Scrum has these three main roles, so we had to figure out who would do what. It was actually kind of fun assigning everyone their "jobs"!</p>

        <div class="role-box">
            <h3>Product Owner (That's Me - Fadhl)</h3>
            <p><strong>My job:</strong> Basically, I'm the one who decides what we're working on and when. I keep track of all the wedding tasks in order of importance, manage our budget (which honestly stresses me out sometimes), and make the final calls when we can't agree on something.</p>
            <p><strong>What this means day-to-day:</strong> I'm constantly updating our to-do list, checking prices, and making sure we're focusing on the right things each week.</p>
        </div>

        <div class="role-box">
            <h3>Scrum Master (My Partner Sarah)</h3>
            <p><strong>Her job:</strong> Sarah keeps us all on track. She's the one who reminds everyone about our daily check-ins, helps solve problems when they come up (like when that one caterer never called us back), and makes sure we're actually following our process instead of just winging it.</p>
            <p><strong>What this means day-to-day:</strong> She's basically our wedding planning coordinator, but with a fancy Scrum title!</p>
        </div>

        <div class="role-box">
            <h3>Development Team (Our Families)</h3>
            <p><strong>Who they are:</strong> My parents, Sarah's parents, my sister, and our two best friends who volunteered to help. They're the ones who actually get stuff done.</p>
            <p><strong>What they do:</strong> Whatever tasks we assign them each week - calling vendors, researching options, giving us feedback on decisions, and basically being our extra hands and brains.</p>
        </div>
    </div>

    <div class="section">
        <h2>Getting Everyone on the Same Page</h2>
        <p>Before we could start planning anything, Sarah and I had to figure out what we actually wanted. And then - this was the tricky part - we had to get both sets of parents to understand and agree with our vision. Anyone who's planned a wedding knows this can be... challenging.</p>

        <p>We spent a whole evening just talking about what our perfect wedding would look like. We're both pretty laid-back people who love being outside, so we knew we didn't want some fancy ballroom thing.</p>

        <h3>What We Decided We Wanted:</h3>
        <p><em>"A relaxed outdoor party with the people we care about most, where everyone can just be themselves and have a good time."</em></p>

        <p>That might sound simple, but getting our families to understand what "relaxed" meant took some work!</p>

        <h3>How We'd Know We Succeeded:</h3>
        <ul>
            <li>Don't go over our $25,000 budget (this was non-negotiable)</li>
            <li>Our guests actually enjoy themselves instead of being bored</li>
            <li>Nothing major goes wrong on the day</li>
            <li>We're not stressed out during our own wedding</li>
            <li>We get some great photos that we'll actually want to look at later</li>
        </ul>
    </div>

    <div class="section">
        <h2>Create a Wedding Backlog</h2>
        <p>Just like in business Scrum, we created a prioritized list of everything that needs to happen for our wedding. The most important items are at the top and will get done first.</p>

        <h3>Our Wedding Backlog (in priority order):</h3>
        <div class="backlog-item">
            <strong>1. Pick a venue</strong> - Most important because everything else depends on this
        </div>
        <div class="backlog-item">
            <strong>2. Select catering/menu</strong> - Major budget item that affects guest experience
        </div>
        <div class="backlog-item">
            <strong>3. Choose photographer</strong> - Good photographers book up quickly
        </div>
        <div class="backlog-item">
            <strong>4. Send invitations</strong> - Guests need time to plan and RSVP
        </div>
        <div class="backlog-item">
            <strong>5. Select wedding attire</strong> - May need alterations and time to order
        </div>
        <div class="backlog-item">
            <strong>6. Order flowers</strong> - Important for photos and atmosphere
        </div>
        <div class="backlog-item">
            <strong>7. Choose music/entertainment</strong> - Sets the mood for reception
        </div>
        <div class="backlog-item">
            <strong>8. Plan honeymoon</strong> - Can be done closer to wedding date
        </div>

        <p><strong>Note:</strong> This list gets re-prioritized as things change. Big items like "send invitations" get broken into smaller tasks like "create guest list," "design invitations," and "mail invitations."</p>
    </div>

    <div class="section">
        <h2>Establish a Ceremony Cadence</h2>
        <p>We decided to use 1-week sprints (including weekends) because wedding planning can move quickly and we wanted to stay on track.</p>

        <h3>Our Weekly Schedule:</h3>
        <ul>
            <li><strong>Sunday Evening:</strong> Sprint Planning - decide what we'll accomplish this week</li>
            <li><strong>Every Day at Dinner:</strong> Daily Scrum - quick check-in on progress</li>
            <li><strong>Saturday Morning:</strong> Sprint Review - show what we completed</li>
            <li><strong>Saturday Afternoon:</strong> Sprint Retrospective - discuss what went well and what to improve</li>
        </ul>

        <p>The key is that we both agreed to this schedule and it's maintainable and consistent. The repeatable flow is what makes Scrum really work!</p>
    </div>

    <div class="section">
        <h2>Plan Your First Sprint</h2>
        <p>For our first sprint, we decided to focus on the most important item from our backlog: selecting our venue.</p>

        <div class="sprint-box">
            <h3>Sprint 1 Goal: "Select the perfect venue"</h3>
            <p><strong>What we committed to accomplish this week:</strong></p>
            <ul>
                <li>Research venues online and create a shortlist</li>
                <li>Schedule meetings with top 3 venue options</li>
                <li>Visit venues and take notes/photos</li>
                <li>Make final selection and negotiate contract</li>
            </ul>

            <p><strong>Daily Check-ins:</strong> Every evening at dinner, we quickly shared what we did that day, what we're doing tomorrow, and if anything is blocking us.</p>
        </div>

        <h3>Sprint 1 Results:</h3>
        <ul>
            <li>✅ Researched 15 venues online</li>
            <li>✅ Visited 3 venues that fit our budget and style</li>
            <li>✅ Selected Riverside Gardens as our venue</li>
            <li>✅ Negotiated a 10% discount and signed contract</li>
            <li>✅ Stayed within budget - venue cost $8,500</li>
        </ul>

        <p><strong>What we learned:</strong> Having a clear weekly goal made it easy to stay focused. The daily check-ins helped us coordinate schedules and support each other.</p>
    </div>

    <div class="section">
        <h2>Use Scrum for Wedding Planning & More</h2>
        <p>After using Scrum for our wedding planning, I can see why it works so well for all kinds of projects. It's actually a lot easier than people think and doesn't require elaborate processes and tools.</p>

        <h3>What Made This Work:</h3>
        <ul>
            <li><strong>Clear roles:</strong> Everyone knew what they were responsible for</li>
            <li><strong>Prioritized backlog:</strong> We always worked on the most important things first</li>
            <li><strong>Regular check-ins:</strong> Daily conversations kept us aligned and caught problems early</li>
            <li><strong>Time-boxed sprints:</strong> Having weekly goals made progress feel achievable</li>
            <li><strong>Continuous improvement:</strong> Weekly retrospectives helped us get better each sprint</li>
        </ul>

        <h3>Key Benefits We Experienced:</h3>
        <ul>
            <li>Reduced stress and overwhelm</li>
            <li>Better family communication and involvement</li>
            <li>Stayed within budget ($24,200 of $25,000)</li>
            <li>Met all deadlines without rushing</li>
            <li>Created stronger relationships through collaboration</li>
        </ul>
    </div>

    <div class="section">
        <h2>What I Actually Learned from This Whole Experience</h2>
        <p>So here's the thing - I went into this thinking I was just trying to be clever by applying something from class to real life. But it ended up teaching me way more than I expected, both about project management and about myself.</p>

        <h3>The Biggest Surprise:</h3>
        <p>I thought using Scrum would just help us stay organized, but what I didn't expect was how much it would bring everyone together. My mom and Sarah's mom, who barely knew each other before, ended up becoming friends through our weekly planning sessions. Even my dad, who usually stays out of this kind of stuff, got really into tracking our progress.</p>

        <p>Instead of wedding planning being this stressful thing that drove everyone crazy, it became something we all looked forward to. Weird, right?</p>

        <h3>Skills I'm Actually Using Now:</h3>
        <ul>
            <li>Breaking big scary projects into smaller, less scary pieces</li>
            <li>Running meetings that don't suck (seriously, this is a skill)</li>
            <li>Managing people's expectations without making them mad</li>
            <li>Looking back at what worked and what didn't to improve next time</li>
            <li>Figuring out what's actually important vs. what just seems urgent</li>
        </ul>

        <h3>Would I Do It Again?</h3>
        <p>Absolutely. What started as 8 months of potential stress became 8 months of actually enjoying the process. We got the wedding we wanted, stayed under budget, and somehow made our families closer in the process. Plus, I learned a bunch of stuff I'm already using in my internship.</p>

        <p>I'm definitely going to use this approach for other big projects in my life. Maybe not everything needs to be "Scrummed," but for anything complex with multiple people involved? Yeah, I'm sold.</p>

        <div style="text-align: center; background-color: #2c3e50; color: white; padding: 20px; border-radius: 10px; margin-top: 30px;">
            <h2>Final Thoughts</h2>
            <p><em>"Looking back, I can't imagine planning our wedding any other way. What could have been months of stress became months of collaboration, and we ended up with not just a great wedding, but stronger relationships and skills I'll use for years."</em></p>
        </div>
    </div>
</body>
</html>
