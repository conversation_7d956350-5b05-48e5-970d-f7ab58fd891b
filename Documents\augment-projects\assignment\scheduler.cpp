/*
 * Operating System Multiple Queue Scheduler Simulator
 * Author: [Your Name]
 * Date: July 25, 2025
 * 
 * This program simulates three different CPU scheduling algorithms:
 * 1. First Come First Serve (FCFS)
 * 2. Shortest Job First (SJF) - Non-preemptive
 * 3. Priority Scheduling - Non-preemptive
 * 
 * The program reads process data from an input file and outputs
 * waiting times and average waiting times for each algorithm.
 */

#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <sstream>
#include <algorithm>
#include <iomanip>

using namespace std;

// Structure to represent a process
// Using struct instead of arrays as per requirements
struct Process {
    int burstTime;      // Time required for process execution
    int priority;       // Process priority (lower number = higher priority)
    int queueId;        // Queue identifier
    int waitingTime;    // Calculated waiting time
    int processId;      // Process identifier for tracking
    
    // Constructor for easy initialization
    Process(int bt, int pr, int qid, int pid) 
        : burstTime(bt), priority(pr), queueId(qid), processId(pid), waitingTime(0) {}
};

// Class to handle the scheduling simulation
class SchedulerSimulator {
private:
    vector<Process> processes;  // Using vector instead of array
    string inputFileName;
    string outputFileName;
    
public:
    // Constructor
    SchedulerSimulator(const string& inputFile, const string& outputFile) 
        : inputFileName(inputFile), outputFileName(outputFile) {}
    
    // Function declarations - you'll implement these
    bool readInputFile();
    void simulateFCFS(vector<Process>& processList);
    void simulateSJF(vector<Process>& processList);
    void simulatePriority(vector<Process>& processList);
    void writeOutput();
    void displayResults();
    
    // Helper functions
    vector<Process> getProcessesByQueue(int queueId);
    double calculateAverageWaitingTime(const vector<Process>& processList);
    void resetWaitingTimes(vector<Process>& processList);
};

// Main function - entry point of the program
int main(int argc, char* argv[]) {
    // Check command line arguments
    if (argc != 3) {
        cout << "Usage: " << argv[0] << " <input_file> <output_file>" << endl;
        cout << "Example: ./cpe351 input.txt output.txt" << endl;
        return 1;
    }
    
    // Create scheduler simulator instance
    SchedulerSimulator simulator(argv[1], argv[2]);
    
    // Read input file
    if (!simulator.readInputFile()) {
        cerr << "Error: Could not read input file: " << argv[1] << endl;
        return 1;
    }
    
    // Display results to screen and write to output file
    simulator.displayResults();
    simulator.writeOutput();
    
    cout << "Simulation completed successfully!" << endl;
    cout << "Results written to: " << argv[2] << endl;
    
    return 0;
}

// Implementation of readInputFile function
// This function reads the input file and parses process data
bool SchedulerSimulator::readInputFile() {
    ifstream inputFile(inputFileName);

    // Check if file opened successfully
    if (!inputFile.is_open()) {
        return false;
    }

    string line;
    int processCounter = 0;

    // Read file line by line
    while (getline(inputFile, line)) {
        // Skip empty lines
        if (line.empty()) {
            continue;
        }

        // Parse the line using stringstream
        stringstream ss(line);
        string token;
        vector<int> values;

        // Split by colon and convert to integers
        while (getline(ss, token, ':')) {
            try {
                values.push_back(stoi(token));
            } catch (const exception& e) {
                cout << "Error parsing line: " << line << endl;
                continue;
            }
        }

        // Validate that we have exactly 3 values
        if (values.size() == 3) {
            // Create new process: burstTime, priority, queueId, processId
            Process newProcess(values[0], values[1], values[2], processCounter);
            processes.push_back(newProcess);
            processCounter++;
        }
    }

    inputFile.close();
    return !processes.empty();
}

// Helper function to get processes by queue ID
vector<Process> SchedulerSimulator::getProcessesByQueue(int queueId) {
    vector<Process> queueProcesses;

    // Go through all processes and find ones with matching queue ID
    for (const Process& p : processes) {
        if (p.queueId == queueId) {
            queueProcesses.push_back(p);
        }
    }

    return queueProcesses;
}

// Helper function to calculate average waiting time
double SchedulerSimulator::calculateAverageWaitingTime(const vector<Process>& processList) {
    if (processList.empty()) {
        return 0.0;
    }

    int totalWaitingTime = 0;
    for (const Process& p : processList) {
        totalWaitingTime += p.waitingTime;
    }

    return static_cast<double>(totalWaitingTime) / processList.size();
}

// Helper function to reset waiting times
void SchedulerSimulator::resetWaitingTimes(vector<Process>& processList) {
    for (Process& p : processList) {
        p.waitingTime = 0;
    }
}

// Implementation of First Come First Serve (FCFS) scheduling
// Processes are executed in the order they appear in the input file
void SchedulerSimulator::simulateFCFS(vector<Process>& processList) {
    // Reset all waiting times first
    resetWaitingTimes(processList);

    // In FCFS, processes are already in correct order (file order)
    // Calculate waiting time for each process
    int currentTime = 0;

    for (int i = 0; i < processList.size(); i++) {
        // Waiting time is the current time when process starts
        processList[i].waitingTime = currentTime;

        // Update current time by adding burst time of current process
        currentTime += processList[i].burstTime;
    }
}

// Implementation of Shortest Job First (SJF) scheduling
// Processes are sorted by burst time (shortest first)
void SchedulerSimulator::simulateSJF(vector<Process>& processList) {
    // Reset all waiting times first
    resetWaitingTimes(processList);

    // Sort processes by burst time (ascending order)
    sort(processList.begin(), processList.end(),
         [](const Process& a, const Process& b) {
             return a.burstTime < b.burstTime;
         });

    // Calculate waiting time for each process
    int currentTime = 0;

    for (int i = 0; i < processList.size(); i++) {
        // Waiting time is the current time when process starts
        processList[i].waitingTime = currentTime;

        // Update current time by adding burst time of current process
        currentTime += processList[i].burstTime;
    }
}

// Implementation of Priority scheduling
// Processes are sorted by priority (lower number = higher priority)
void SchedulerSimulator::simulatePriority(vector<Process>& processList) {
    // Reset all waiting times first
    resetWaitingTimes(processList);

    // Sort processes by priority (ascending order - lower number = higher priority)
    // If priorities are equal, maintain original order (FCFS for ties)
    sort(processList.begin(), processList.end(),
         [](const Process& a, const Process& b) {
             if (a.priority == b.priority) {
                 return a.processId < b.processId; // FCFS for same priority
             }
             return a.priority < b.priority;
         });

    // Calculate waiting time for each process
    int currentTime = 0;

    for (int i = 0; i < processList.size(); i++) {
        // Waiting time is the current time when process starts
        processList[i].waitingTime = currentTime;

        // Update current time by adding burst time of current process
        currentTime += processList[i].burstTime;
    }
}

// Function to display results on screen
void SchedulerSimulator::displayResults() {
    cout << "Operating System Multiple Queue Scheduler Results" << endl;
    cout << "=================================================" << endl;

    // Find all unique queue IDs
    vector<int> queueIds;
    for (const Process& p : processes) {
        bool found = false;
        for (int id : queueIds) {
            if (id == p.queueId) {
                found = true;
                break;
            }
        }
        if (!found) {
            queueIds.push_back(p.queueId);
        }
    }

    // Sort queue IDs for consistent output
    sort(queueIds.begin(), queueIds.end());

    // Process each queue with each algorithm
    for (int queueId : queueIds) {
        vector<Process> queueProcesses = getProcessesByQueue(queueId);

        if (queueProcesses.empty()) {
            continue;
        }

        cout << "\nQueue " << queueId << " Results:" << endl;

        // FCFS Algorithm (Algorithm 1)
        simulateFCFS(queueProcesses);
        cout << queueId << ":1:";
        for (int i = 0; i < queueProcesses.size(); i++) {
            cout << queueProcesses[i].waitingTime;
            if (i < queueProcesses.size() - 1) cout << ":";
        }
        cout << ":" << fixed << setprecision(2) << calculateAverageWaitingTime(queueProcesses) << endl;

        // SJF Algorithm (Algorithm 2)
        queueProcesses = getProcessesByQueue(queueId); // Reset order
        simulateSJF(queueProcesses);
        cout << queueId << ":2:";
        for (int i = 0; i < queueProcesses.size(); i++) {
            cout << queueProcesses[i].waitingTime;
            if (i < queueProcesses.size() - 1) cout << ":";
        }
        cout << ":" << fixed << setprecision(2) << calculateAverageWaitingTime(queueProcesses) << endl;

        // Priority Algorithm (Algorithm 3)
        queueProcesses = getProcessesByQueue(queueId); // Reset order
        simulatePriority(queueProcesses);
        cout << queueId << ":3:";
        for (int i = 0; i < queueProcesses.size(); i++) {
            cout << queueProcesses[i].waitingTime;
            if (i < queueProcesses.size() - 1) cout << ":";
        }
        cout << ":" << fixed << setprecision(2) << calculateAverageWaitingTime(queueProcesses) << endl;
    }
}

// Function to write results to output file
void SchedulerSimulator::writeOutput() {
    ofstream outputFile(outputFileName);

    if (!outputFile.is_open()) {
        cerr << "Error: Could not create output file: " << outputFileName << endl;
        return;
    }

    // Find all unique queue IDs
    vector<int> queueIds;
    for (const Process& p : processes) {
        bool found = false;
        for (int id : queueIds) {
            if (id == p.queueId) {
                found = true;
                break;
            }
        }
        if (!found) {
            queueIds.push_back(p.queueId);
        }
    }

    // Sort queue IDs for consistent output
    sort(queueIds.begin(), queueIds.end());

    // Process each queue with each algorithm
    for (int queueId : queueIds) {
        vector<Process> queueProcesses = getProcessesByQueue(queueId);

        if (queueProcesses.empty()) {
            continue;
        }

        // FCFS Algorithm (Algorithm 1)
        simulateFCFS(queueProcesses);
        outputFile << queueId << ":1:";
        for (int i = 0; i < queueProcesses.size(); i++) {
            outputFile << queueProcesses[i].waitingTime;
            if (i < queueProcesses.size() - 1) outputFile << ":";
        }
        outputFile << ":" << fixed << setprecision(2) << calculateAverageWaitingTime(queueProcesses) << endl;

        // SJF Algorithm (Algorithm 2)
        queueProcesses = getProcessesByQueue(queueId); // Reset order
        simulateSJF(queueProcesses);
        outputFile << queueId << ":2:";
        for (int i = 0; i < queueProcesses.size(); i++) {
            outputFile << queueProcesses[i].waitingTime;
            if (i < queueProcesses.size() - 1) outputFile << ":";
        }
        outputFile << ":" << fixed << setprecision(2) << calculateAverageWaitingTime(queueProcesses) << endl;

        // Priority Algorithm (Algorithm 3)
        queueProcesses = getProcessesByQueue(queueId); // Reset order
        simulatePriority(queueProcesses);
        outputFile << queueId << ":3:";
        for (int i = 0; i < queueProcesses.size(); i++) {
            outputFile << queueProcesses[i].waitingTime;
            if (i < queueProcesses.size() - 1) outputFile << ":";
        }
        outputFile << ":" << fixed << setprecision(2) << calculateAverageWaitingTime(queueProcesses) << endl;
    }

    outputFile.close();
}
