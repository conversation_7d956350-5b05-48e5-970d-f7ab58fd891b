<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Completion - Udemy</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap');

        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: 'Open Sans', sans-serif;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .certificate {
            width: 842px;
            height: 595px;
            background: linear-gradient(135deg, #e8f4f8 0%, #d1e7dd 50%, #f8f9fa 100%);
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .border-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(52, 152, 219, 0.1) 10px, rgba(52, 152, 219, 0.1) 20px),
                repeating-linear-gradient(-45deg, transparent, transparent 10px, rgba(52, 152, 219, 0.1) 10px, rgba(52, 152, 219, 0.1) 20px);
            border: 15px solid;
            border-image: linear-gradient(45deg, #3498db, #2980b9, #3498db, #2980b9) 1;
        }

        .inner-border {
            position: absolute;
            top: 25px;
            left: 25px;
            right: 25px;
            bottom: 25px;
            border: 2px solid #3498db;
            background: rgba(255, 255, 255, 0.9);
        }

        .content {
            position: absolute;
            top: 50px;
            left: 50px;
            right: 50px;
            bottom: 50px;
            text-align: center;
            z-index: 10;
        }

        .title {
            font-size: 48px;
            font-weight: 300;
            color: #2c3e50;
            margin: 40px 0 30px 0;
            font-style: italic;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 18px;
            color: #34495e;
            margin-bottom: 40px;
            font-weight: 400;
        }

        .recipient-name {
            font-size: 36px;
            font-weight: 700;
            color: #2c3e50;
            margin: 30px 0;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        .course-info {
            font-size: 16px;
            color: #34495e;
            margin: 30px 0;
            font-weight: 400;
        }

        .course-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin: 10px 0 30px 0;
        }

        .date {
            font-size: 16px;
            color: #34495e;
            margin: 30px 0;
        }

        .instructor-section {
            position: absolute;
            bottom: 60px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }

        .instructor-line {
            width: 200px;
            height: 1px;
            background: #bdc3c7;
            margin: 0 auto 10px;
        }

        .instructor-label {
            font-size: 12px;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .udemy-logo {
            position: absolute;
            bottom: 80px;
            right: 80px;
            width: 80px;
            height: 30px;
            background: #ec5252;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            letter-spacing: 1px;
        }

        .certificate-id {
            position: absolute;
            bottom: 30px;
            left: 50px;
            font-size: 10px;
            color: #95a5a6;
        }

        .decorative-seal {
            position: absolute;
            bottom: 100px;
            left: 80px;
            width: 60px;
            height: 60px;
            border: 3px solid #3498db;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(52, 152, 219, 0.1);
            font-size: 10px;
            color: #3498db;
            text-align: center;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="border-pattern"></div>
        <div class="inner-border"></div>

        <div class="content">
            <h1 class="title">Certificate of Completion</h1>

            <p class="subtitle">This is to certify that</p>

            <h2 class="recipient-name">FADHL ALSHARIF</h2>

            <p class="course-info">successfully completed</p>

            <h3 class="course-title">Complete Python Bootcamp: Go from zero to hero in Python 3</h3>

            <p class="date">course on July 21, 2025</p>

            <div class="instructor-section">
                <div class="instructor-line"></div>
                <p class="instructor-label">Instructor</p>
            </div>
        </div>

        <div class="udemy-logo">udemy</div>

        <div class="decorative-seal">
            <div>VERIFIED<br>CERTIFICATE</div>
        </div>

        <div class="certificate-id">Certificate ID: UC-PYTHON-2025-FADHL</div>
    </div>
</body>
</html>
