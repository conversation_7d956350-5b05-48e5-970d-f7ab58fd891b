<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wedding Planning Scrum Project Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .section {
            background-color: white;
            padding: 25px;
            margin-bottom: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5em;
        }
        h2 {
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-top: 0;
        }
        h3 {
            color: #555;
            margin-top: 25px;
        }
        .project-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .info-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }
        .info-card p {
            margin: 0;
            font-size: 1.3em;
            font-weight: bold;
        }
        .personnel-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .role-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
        }
        .role-card h4 {
            margin: 0 0 15px 0;
            font-size: 1.2em;
        }
        .role-card ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .epic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }
        .epic-card {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: #333;
            padding: 20px;
            border-radius: 10px;
        }
        .epic-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .priority-high { border-left: 5px solid #e74c3c; }
        .priority-medium { border-left: 5px solid #f39c12; }
        .priority-low { border-left: 5px solid #27ae60; }
        .story-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .story-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .story-points {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .acceptance-criteria {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .sprint-timeline {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .sprint-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            color: #333;
        }
        .ceremonies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .ceremony-card {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .dod-list {
            background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
        }
        .dod-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .print-btn {
            background: #667eea;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
        }
        .print-btn:hover {
            background: #5a6fd8;
        }
        @media print {
            body { background-color: white; }
            .section { box-shadow: none; }
            .print-btn { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Wedding Planning Scrum Project</h1>
        <p>MVP Implementation Using Scrum Methodology</p>
        <button class="print-btn" onclick="window.print()">Print Project</button>
        <button class="print-btn" onclick="window.location.reload()">Refresh</button>
    </div>

    <div class="section">
        <h2>📋 Project Overview</h2>
        <div class="project-info">
            <div class="info-card">
                <h4>Project Type</h4>
                <p>Scrum MVP</p>
            </div>
            <div class="info-card">
                <h4>Duration</h4>
                <p>8 Months</p>
            </div>
            <div class="info-card">
                <h4>Budget</h4>
                <p>$25,000</p>
            </div>
            <div class="info-card">
                <h4>Framework</h4>
                <p>Agile Scrum</p>
            </div>
        </div>
        <p><strong>Description:</strong> MVP wedding planning project using Scrum framework to organize and execute a successful wedding within budget and timeline constraints.</p>
    </div>

    <div class="section">
        <h2>👥 Scrum Team Personnel</h2>
        <div class="personnel-grid">
            <div class="role-card">
                <h4>🎯 Product Owner</h4>
                <p><strong>Sarah Johnson</strong> (Wedding Planner)</p>
                <ul>
                    <li>Prioritizing wedding features and managing budget</li>
                    <li>Making final decisions on vendor selections and design choices</li>
                    <li>Maintaining vision alignment throughout planning process</li>
                    <li>Defining requirements and acceptance criteria for all wedding elements</li>
                </ul>
            </div>
            <div class="role-card">
                <h4>🏃‍♂️ Scrum Master</h4>
                <p><strong>Michael Johnson</strong> (Wedding Coordinator)</p>
                <ul>
                    <li>Facilitating all planning meetings and daily check-ins</li>
                    <li>Removing obstacles and resolving conflicts between stakeholders</li>
                    <li>Ensuring team stays focused on sprint goals and timelines</li>
                    <li>Coordinating communication between all team members and vendors</li>
                </ul>
            </div>
            <div class="role-card">
                <h4>👨‍👩‍👧‍👦 Development Team</h4>
                <p><strong>Wedding Planning Team</strong></p>
                <ul>
                    <li><strong>Jessica (Maid of Honor):</strong> Vendor research, décor planning, day-of coordination</li>
                    <li><strong>David (Best Man):</strong> Music coordination, transportation, groomsmen management</li>
                    <li><strong>Sarah's Mom:</strong> Guest management, traditional elements, family coordination</li>
                    <li><strong>Michael's Dad:</strong> Venue logistics, vendor negotiations, technical setup</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>📊 Project Epics</h2>
        <div class="epic-grid">
            <div class="epic-card priority-high">
                <h4>🏛️ EPIC-001: Venue Selection and Setup</h4>
                <p><strong>Priority:</strong> High | <strong>Story Points:</strong> 50</p>
                <p>Secure and prepare the perfect venue for ceremony and reception</p>
            </div>
            <div class="epic-card priority-high">
                <h4>🤝 EPIC-002: Vendor Management</h4>
                <p><strong>Priority:</strong> High | <strong>Story Points:</strong> 45</p>
                <p>Book and coordinate all essential wedding vendors</p>
            </div>
            <div class="epic-card priority-medium">
                <h4>👥 EPIC-003: Guest Experience</h4>
                <p><strong>Priority:</strong> Medium | <strong>Story Points:</strong> 35</p>
                <p>Manage guest communications and experience planning</p>
            </div>
            <div class="epic-card priority-medium">
                <h4>💍 EPIC-004: Personal Preparation</h4>
                <p><strong>Priority:</strong> Medium | <strong>Story Points:</strong> 30</p>
                <p>Handle couple's personal wedding preparations</p>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>📝 User Stories</h2>

        <div class="story-card priority-high">
            <div class="story-header">
                <h4>WP-001: Research Wedding Venues</h4>
                <span class="story-points">8 pts</span>
            </div>
            <p><strong>As a couple,</strong> I want to research venues within 30 miles so that we can find options that fit our guest count and budget</p>
            <div class="acceptance-criteria">
                <strong>Acceptance Criteria:</strong>
                <ul>
                    <li>List of 15+ venues with capacity, pricing, availability</li>
                    <li>Venues categorized by style (outdoor, indoor, hybrid)</li>
                    <li>Contact information and booking requirements documented</li>
                </ul>
            </div>
        </div>

        <div class="story-card priority-high">
            <div class="story-header">
                <h4>WP-002: Visit Top Venues</h4>
                <span class="story-points">13 pts</span>
            </div>
            <p><strong>As a bride,</strong> I want to visit top 5 venues in person so that I can visualize our ceremony there</p>
            <div class="acceptance-criteria">
                <strong>Acceptance Criteria:</strong>
                <ul>
                    <li>Completed venue visit checklist for each location</li>
                    <li>Photos taken of each venue setup</li>
                    <li>Vendor restrictions and policies documented</li>
                </ul>
            </div>
        </div>

        <div class="story-card priority-high">
            <div class="story-header">
                <h4>WP-004: Book Wedding Photographer</h4>
                <span class="story-points">8 pts</span>
            </div>
            <p><strong>As a couple,</strong> I want to review photographer portfolios and book our photographer so that we capture our special day beautifully</p>
            <div class="acceptance-criteria">
                <strong>Acceptance Criteria:</strong>
                <ul>
                    <li>Review 15+ photographer portfolios</li>
                    <li>Meet with top 3 finalists</li>
                    <li>Check references from recent clients</li>
                    <li>Sign contract with chosen photographer</li>
                </ul>
            </div>
        </div>

        <div class="story-card priority-high">
            <div class="story-header">
                <h4>WP-005: Select Wedding Catering</h4>
                <span class="story-points">10 pts</span>
            </div>
            <p><strong>As a couple,</strong> I want to taste-test menu options from different caterers so that we provide delicious food for our guests</p>
            <div class="acceptance-criteria">
                <strong>Acceptance Criteria:</strong>
                <ul>
                    <li>Research 8+ catering companies</li>
                    <li>Schedule tastings with top 4 caterers</li>
                    <li>Accommodate dietary restrictions</li>
                    <li>Finalize menu and service style</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🏃‍♂️ Sprint Timeline</h2>
        <div class="sprint-timeline">
            <div class="sprint-card">
                <h4>Sprint 1: Venue Selection</h4>
                <p><strong>Duration:</strong> March 1-14, 2024 (2 weeks)</p>
                <p><strong>Goal:</strong> Secure the perfect venue that aligns with our vision and budget</p>
                <p><strong>Capacity:</strong> 34 hours</p>
                <p><strong>Stories:</strong> WP-001, WP-002, WP-003</p>
            </div>
            <div class="sprint-card">
                <h4>Sprint 2: Key Vendor Selection</h4>
                <p><strong>Duration:</strong> March 15-28, 2024 (2 weeks)</p>
                <p><strong>Goal:</strong> Book photographer, caterer, and florist for wedding day</p>
                <p><strong>Capacity:</strong> 32 hours</p>
                <p><strong>Stories:</strong> WP-004, WP-005, WP-006</p>
            </div>
            <div class="sprint-card">
                <h4>Sprint 3: Guest Management</h4>
                <p><strong>Duration:</strong> March 29 - April 11, 2024 (2 weeks)</p>
                <p><strong>Goal:</strong> Finalize guest list and send invitations</p>
                <p><strong>Capacity:</strong> 28 hours</p>
                <p><strong>Stories:</strong> WP-007, WP-008</p>
            </div>
            <div class="sprint-card">
                <h4>Sprint 4: Personal Preparation</h4>
                <p><strong>Duration:</strong> April 12-25, 2024 (2 weeks)</p>
                <p><strong>Goal:</strong> Complete personal wedding preparations</p>
                <p><strong>Capacity:</strong> 30 hours</p>
                <p><strong>Stories:</strong> WP-009, WP-010</p>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🔄 Scrum Ceremonies</h2>
        <div class="ceremonies-grid">
            <div class="ceremony-card">
                <h4>Sprint Planning</h4>
                <p><strong>Frequency:</strong> Every 2 weeks</p>
                <p><strong>Duration:</strong> 2 hours</p>
            </div>
            <div class="ceremony-card">
                <h4>Daily Scrum</h4>
                <p><strong>Frequency:</strong> Daily</p>
                <p><strong>Duration:</strong> 15 minutes</p>
                <p><strong>Time:</strong> 7:00 PM</p>
            </div>
            <div class="ceremony-card">
                <h4>Sprint Review</h4>
                <p><strong>Frequency:</strong> End of sprint</p>
                <p><strong>Duration:</strong> 1 hour</p>
            </div>
            <div class="ceremony-card">
                <h4>Sprint Retrospective</h4>
                <p><strong>Frequency:</strong> End of sprint</p>
                <p><strong>Duration:</strong> 45 minutes</p>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>✅ Definition of Done</h2>
        <div class="dod-list">
            <ul>
                <li>All acceptance criteria met</li>
                <li>Stakeholder approval received</li>
                <li>Budget impact documented</li>
                <li>Timeline updated</li>
                <li>Dependencies resolved</li>
                <li>Documentation completed</li>
            </ul>
        </div>
    </div>
</body>
</html>
